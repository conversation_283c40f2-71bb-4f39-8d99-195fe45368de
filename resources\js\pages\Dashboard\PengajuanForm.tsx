import { Head, useForm } from '@inertiajs/react'
import { FormEventHandler, useState } from 'react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Upload, FileText, CheckCircle, AlertCircle, X } from 'lucide-react'
import { PegawaiCache } from '@/types'
import { getDokumenWajib, formatFileSize, validateFile } from '@/lib/utils'
import FileUploadField from '@/components/FileUploadField'
import { useNotification } from '@/components/NotificationProvider'

interface Props {
  pegawai: PegawaiCache
  jenis_pensiun_options: string[]
}

interface FileState {
  [jenisDokumen: string]: File | null
}

export default function PengajuanForm({ pegawai, jenis_pensiun_options }: Props) {
  const [selectedJenisPensiun, setSelectedJenisPensiun] = useState<string>('')
  const [fileStates, setFileStates] = useState<FileState>({})
  const [dokumenWajib, setDokumenWajib] = useState<string[]>([])
  const { showSuccess, showError, showWarning } = useNotification()

  const { data, setData, post, processing, errors } = useForm({
    nip: pegawai.nip,
    jenis_pensiun: '',
    dokumen: {} as Record<string, File>
  })

  const handleJenisPensiunChange = (value: string) => {
    setSelectedJenisPensiun(value)
    setData('jenis_pensiun', value)
    setDokumenWajib(getDokumenWajib(value))
    setFileStates({}) // Reset uploaded files when changing jenis pensiun
  }

  const handleFileChange = (jenisDokumen: string, file: File | null) => {
    setFileStates(prev => ({
      ...prev,
      [jenisDokumen]: file
    }))

    // Update form data
    const newDokumen = { ...data.dokumen }
    if (file) {
      newDokumen[jenisDokumen] = file
      showSuccess(`File ${jenisDokumen} berhasil dipilih`, 'File Terupload')
    } else {
      delete newDokumen[jenisDokumen]
    }
    setData('dokumen', newDokumen)
  }

  const isDokumenComplete = () => {
    return dokumenWajib.every(jenis => fileStates[jenis] !== null && fileStates[jenis] !== undefined)
  }

  const submit: FormEventHandler = (e) => {
    e.preventDefault()

    // Validasi dokumen sebelum submit
    if (!isDokumenComplete()) {
      showWarning('Harap upload semua dokumen wajib sebelum menyimpan', 'Dokumen Tidak Lengkap')
      return
    }

    post(route('kabupaten.pengajuan.store'), {
      forceFormData: true,
      preserveScroll: true,
      onSuccess: () => {
        showSuccess(
          'Pengajuan pensiun berhasil disimpan sebagai draft. Anda dapat melengkapi atau mengedit sebelum diajukan.',
          'Draft Tersimpan'
        )
      },
      onError: (errors: Record<string, string>) => {
        const errorMessages = Object.values(errors).flat()
        showError(
          errorMessages.length > 0
            ? errorMessages.join(', ')
            : 'Terjadi kesalahan saat menyimpan pengajuan',
          'Gagal Menyimpan'
        )
      }
    })
  }

  return (
    <AppLayout title={`Pengajuan Pensiun - ${pegawai.nama}`}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Pengajuan Pensiun</h1>
          <p className="text-gray-600">Buat pengajuan pensiun untuk pegawai</p>
        </div>

        <form onSubmit={submit} className="space-y-6">
          {/* Data Pegawai */}
          <Card>
            <CardHeader>
              <CardTitle>Data Pegawai</CardTitle>
              <CardDescription>
                Informasi pegawai yang akan mengajukan pensiun
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>NIP</Label>
                  <Input value={pegawai.nip} disabled />
                </div>
                <div>
                  <Label>Nama Lengkap</Label>
                  <Input value={pegawai.nama} disabled />
                </div>
                <div>
                  <Label>Golongan</Label>
                  <Input value={pegawai.golongan} disabled />
                </div>
                <div>
                  <Label>TMT Pensiun</Label>
                  <Input value={new Date(pegawai.tmt_pensiun).toLocaleDateString('id-ID')} disabled />
                </div>
                <div className="md:col-span-2">
                  <Label>Unit Kerja</Label>
                  <Input value={pegawai.unit_kerja} disabled />
                </div>
                <div className="md:col-span-2">
                  <Label>Jabatan</Label>
                  <Input value={pegawai.jabatan} disabled />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Jenis Pensiun */}
          <Card>
            <CardHeader>
              <CardTitle>Jenis Pensiun</CardTitle>
              <CardDescription>
                Pilih jenis pensiun yang sesuai
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="jenis_pensiun">Jenis Pensiun *</Label>
                <Select value={selectedJenisPensiun} onValueChange={handleJenisPensiunChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih jenis pensiun" />
                  </SelectTrigger>
                  <SelectContent>
                    {jenis_pensiun_options.map((jenis) => (
                      <SelectItem key={jenis} value={jenis}>
                        {jenis === 'BUP' && 'Batas Usia Pensiun (BUP)'}
                        {jenis === 'Sakit' && 'Pensiun Sakit'}
                        {jenis === 'Janda/Duda' && 'Pensiun Janda/Duda'}
                        {jenis === 'APS' && 'Alih Tugas (APS)'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.jenis_pensiun && (
                  <p className="text-sm text-red-600">{errors.jenis_pensiun}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Upload Dokumen */}
          {selectedJenisPensiun && dokumenWajib.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Upload Dokumen
                </CardTitle>
                <CardDescription>
                  Upload dokumen yang diperlukan untuk jenis pensiun {selectedJenisPensiun}. 
                  Maksimal ukuran file 350KB. Format yang diterima: PDF, DOC, DOCX, JPG, PNG.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {dokumenWajib.map((jenisDokumen) => (
                  <FileUploadField
                    key={jenisDokumen}
                    label={jenisDokumen}
                    jenis_dokumen={jenisDokumen}
                    file={fileStates[jenisDokumen] || null}
                    onFileChange={(file) => handleFileChange(jenisDokumen, file)}
                    required={true}
                    maxSizeKB={350}
                  />
                ))}
              </CardContent>
            </Card>
          )}

          {/* Submit */}
          {selectedJenisPensiun && (
            <Card>
              <CardContent className="pt-6">
                {!isDokumenComplete() && (
                  <Alert className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Silakan upload semua dokumen wajib sebelum menyimpan pengajuan.
                    </AlertDescription>
                  </Alert>
                )}

                <Separator className="mb-4" />

                <div className="flex gap-4">
                  <Button
                    type="submit"
                    disabled={processing || !isDokumenComplete()}
                    className="flex items-center gap-2"
                  >
                    {processing ? 'Menyimpan...' : 'Simpan sebagai Draft'}
                  </Button>
                  
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => window.history.back()}
                  >
                    Batal
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </form>
      </div>
    </AppLayout>
  )
}
