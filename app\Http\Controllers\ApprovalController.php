<?php

namespace App\Http\Controllers;

use App\Models\PengajuanPensiun;
use App\Models\LogAktivitas;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class ApprovalController extends Controller
{
    /**
     * Tampilkan daftar pengajuan untuk review Kanwil
     */
    public function index(Request $request): Response
    {
        $query = PengajuanPensiun::with(['pegawai', 'user', 'penyetuju'])
            ->latest();

        // Filter berdasarkan status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter berdasarkan jenis pensiun
        if ($request->jenis_pensiun) {
            $query->where('jenis_pensiun', $request->jenis_pensiun);
        }

        // Search
        if ($request->search) {
            $query->whereHas('pegawai', function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->search . '%')
                  ->orWhere('nip', 'like', '%' . $request->search . '%');
            });
        }

        $pengajuanList = $query->paginate(20);

        return Inertia::render('Kanwil/PengajuanList', [
            'pengajuan_list' => $pengajuanList,
            'filters' => $request->only(['status', 'jenis_pensiun', 'search']),
            'status_options' => ['Draft', 'Diajukan', 'Disetujui', 'Ditolak'],
            'jenis_pensiun_options' => ['BUP', 'Sakit', 'Janda/Duda', 'APS']
        ]);
    }



    /**
     * Tampilkan detail pengajuan untuk review
     */
    public function show(int $id): Response
    {
        $pengajuan = PengajuanPensiun::with(['pegawai', 'dokumen', 'user', 'penyetuju'])
            ->findOrFail($id);

        return Inertia::render('Dashboard/PengajuanDetail', [
            'pengajuan' => $pengajuan,
            'dokumen_wajib' => $pengajuan->getDokumenWajib(),
            'can_approve' => $pengajuan->canBeApproved(),
            'log_aktivitas' => $pengajuan->logAktivitas()->with('user')->latest()->get()
        ]);
    }

    /**
     * Setujui pengajuan pensiun
     */
    public function approve(Request $request, int $id): RedirectResponse
    {
        $pengajuan = PengajuanPensiun::findOrFail($id);

        if (!$pengajuan->canBeApproved()) {
            return back()->withErrors(['error' => 'Pengajuan tidak dapat disetujui']);
        }

        $pengajuan->update([
            'status' => 'Disetujui',
            'disetujui_oleh' => auth()->id(),
            'tanggal_disetujui' => now(),
            'catatan_penolakan' => null // Clear rejection note if any
        ]);

        // Log aktivitas
        LogAktivitas::logActivity(
            'menyetujui',
            'pengajuan_pensiun',
            $pengajuan->id,
            "Menyetujui pengajuan pensiun {$pengajuan->jenis_pensiun} untuk pegawai {$pengajuan->pegawai->nama}"
        );

        return redirect()->route('kanwil.dashboard')
            ->with('success', 'Pengajuan pensiun berhasil disetujui');
    }

    /**
     * Tolak pengajuan pensiun
     */
    public function reject(Request $request, int $id): RedirectResponse
    {
        $request->validate([
            'catatan_penolakan' => 'required|string|min:10|max:500'
        ]);

        $pengajuan = PengajuanPensiun::findOrFail($id);

        if ($pengajuan->status !== 'Diajukan') {
            return back()->withErrors(['error' => 'Pengajuan tidak dapat ditolak']);
        }

        $pengajuan->update([
            'status' => 'Ditolak',
            'catatan_penolakan' => $request->catatan_penolakan,
            'disetujui_oleh' => auth()->id(),
            'tanggal_disetujui' => now()
        ]);

        // Log aktivitas
        LogAktivitas::logActivity(
            'menolak',
            'pengajuan_pensiun',
            $pengajuan->id,
            "Menolak pengajuan pensiun dengan alasan: {$request->catatan_penolakan}"
        );

        return redirect()->route('kanwil.dashboard')
            ->with('success', 'Pengajuan pensiun berhasil ditolak');
    }


}