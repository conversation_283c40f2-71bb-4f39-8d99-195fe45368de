import './bootstrap'
import '../css/app.css'

import { createRoot } from 'react-dom/client'
import { createInertiaApp } from '@inertiajs/react'
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers'
import { Toaster } from '@/components/ui/sonner'
import { NotificationProvider } from '@/components/NotificationProvider'

const appName = import.meta.env.VITE_APP_NAME || 'Laravel'

createInertiaApp({
  title: (title) => `${title} - ${appName}`,
  resolve: (name) =>
    resolvePageComponent(
      `./pages/${name}.tsx`,
      import.meta.glob('./pages/**/*.tsx'),
    ),
  setup({ el, App, props }) {
    const root = createRoot(el)

    root.render(
      <NotificationProvider>
        <App {...props} />
        <Toaster richColors closeButton />
      </NotificationProvider>
    )
  },
  progress: {
    color: '#4B5563',
  },
})
