<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pengajuan_pensiun', function (Blueprint $table) {
            $table->id();
            $table->string('nip'); // NIP pegawai dari cache
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // User yang mengajukan
            $table->enum('jenis_pensiun', ['BUP', 'Sakit', 'Janda/Duda', 'APS']);
            $table->enum('status', ['Draft', 'Diajukan', 'Disetujui', 'Ditolak'])->default('Draft');
            $table->text('catatan_penolakan')->nullable();
            $table->foreignId('disetujui_oleh')->nullable()->constrained('users')->onDelete('set null');
            $table->dateTime('tanggal_disetujui')->nullable();
            $table->timestamps();
            
            $table->index(['nip', 'status']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pengajuan_pensiun');
    }
};
