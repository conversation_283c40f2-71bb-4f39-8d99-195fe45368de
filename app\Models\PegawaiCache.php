<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PegawaiCache extends Model
{
    protected $table = 'pegawai_cache';
    
    protected $fillable = [
        'nip',
        'nama',
        'golongan',
        'tmt_pensiun',
        'unit_kerja',
        'induk_unit',
        'jabatan',

        'jenis_pegawai',
        'aktif',
        'last_sync'
    ];

    protected $casts = [
        'tmt_pensiun' => 'datetime',
        'last_sync' => 'datetime',
        'aktif' => 'boolean',
    ];

    // Scope untuk pegawai yang sudah TMT pensiun atau hari ini
    public function scopeTmtPensiunHariIni($query)
    {
        return $query->where('tmt_pensiun', '<=', Carbon::today())
                     ->where('aktif', true);
    }

    // Scope untuk pegawai aktif
    public function scopeAktif($query)
    {
        return $query->where('aktif', true);
    }

    // Relationship dengan pengajuan pensiun
    public function pengajuanPensiun()
    {
        return $this->hasMany(PengajuanPensiun::class, 'nip', 'nip');
    }

    // Check apakah pegawai sudah punya pengajuan aktif
    public function hasPengajuanAktif()
    {
        return $this->pengajuanPensiun()
                    ->whereIn('status', ['Draft', 'Diajukan'])
                    ->exists();
    }
}
