<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PegawaiCache extends Model
{
    protected $table = 'pegawai_cache';
    
    protected $fillable = [
        'nip',
        'nama',
        'golongan',
        'tmt_pensiun',
        'unit_kerja',
        'induk_unit',
        'jabatan',
        'kantor_id',
        'jenis_pegawai',
        'aktif',
        'last_sync'
    ];

    protected $casts = [
        'tmt_pensiun' => 'datetime',
        'last_sync' => 'datetime',
        'aktif' => 'boolean',
    ];

    // Scope untuk pegawai yang sudah pensiun (TMT pensiun <= hari ini)
    public function scopeSudahPensiun($query)
    {
        return $query->whereNotNull('tmt_pensiun')
                     ->where('tmt_pensiun', '<=', Carbon::today())
                     ->where('aktif', true);
    }
    
    // Alias untuk backward compatibility
    public function scopeTmtPensiunHariIni($query)
    {
        return $this->scopeSudahPensiun($query);
    }

    // Scope untuk pegawai aktif
    public function scopeAktif($query)
    {
        return $query->where('aktif', true);
    }

    // Relationship dengan pengajuan pensiun
    public function pengajuanPensiun()
    {
        return $this->hasMany(PengajuanPensiun::class, 'nip', 'nip');
    }

    // Check apakah pegawai sudah punya pengajuan aktif
    public function hasPengajuanAktif()
    {
        return $this->pengajuanPensiun()
                    ->whereIn('status', ['Draft', 'Diajukan'])
                    ->exists();
    }
}
