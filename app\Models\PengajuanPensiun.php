<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PengajuanPensiun extends Model
{
    protected $table = 'pengajuan_pensiun';
    
    protected $fillable = [
        'nip',
        'user_id',
        'jenis_pensiun',
        'status',
        'catatan_penolakan',
        'disetujui_oleh',
        'tanggal_disetujui'
    ];

    protected $casts = [
        'tanggal_disetujui' => 'datetime',
    ];

    // Relationship dengan User (yang mengajukan)
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relationship dengan User (yang menyetujui)
    public function penyetuju()
    {
        return $this->belongsTo(User::class, 'disetujui_oleh');
    }

    // Relationship dengan PegawaiCache
    public function pegawai()
    {
        return $this->belongsTo(PegawaiCache::class, 'nip', 'nip');
    }

    // Relationship dengan Dokumen
    public function dokumen()
    {
        return $this->hasMany(Dokumen::class, 'pengajuan_id');
    }

    // Relationship dengan LogAktivitas
    public function logAktivitas()
    {
        return $this->hasMany(LogAktivitas::class, 'entitas_id')
                    ->where('entitas', 'pengajuan_pensiun');
    }

    // Scope untuk status tertentu
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Scope untuk jenis pensiun tertentu
    public function scopeJenisPensiun($query, $jenis)
    {
        return $query->where('jenis_pensiun', $jenis);
    }

    // Check apakah bisa disetujui
    public function canBeApproved()
    {
        return $this->status === 'Diajukan' && $this->dokumen()->count() > 0;
    }

    // Get dokumen yang wajib berdasarkan jenis pensiun
    public function getDokumenWajib()
    {
        $dokumenWajib = [
            'Pengantar', 'DPCP', 'SK CPNS', 'SKKP Terakhir', 'Super HD',
            'Super Pidana', 'Pas Foto', 'Buku Nikah', 'Kartu Keluarga', 'SKP Terakhir'
        ];

        $dokumenTambahan = match($this->jenis_pensiun) {
            'Sakit' => ['Surat Keterangan Sakit'],
            'Janda/Duda' => ['Akta Kematian', 'Suket Janda/Duda', 'Pas Foto Pasangan'],
            'APS' => ['Surat Usul Pemberhentian', 'Surat Permohonan PPS'],
            default => []
        };

        return array_merge($dokumenWajib, $dokumenTambahan);
    }
}
