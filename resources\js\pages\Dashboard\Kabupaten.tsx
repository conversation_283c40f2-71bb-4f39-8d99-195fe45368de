import { Head, usePage } from '@inertiajs/react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Search, Plus, Calendar, Users, FileText, Clock } from 'lucide-react'
import { useState } from 'react'
import { formatDate, getStatusBadgeVariant, getStatusLabel } from '@/lib/utils'

interface PegawaiCache {
  id: number
  nip: string
  nama: string
  golongan: string
  tmt_pensiun: string
  unit_kerja: string
  jabatan: string
  jenis_pegawai: string
  has_pengajuan_aktif: boolean
  pengajuan_status?: string
}

interface DashboardStats {
  total_pegawai_tmt: number
  total_pengajuan_draft: number
  total_pengajuan_diajukan: number
  total_pengajuan_disetujui: number
}

interface Props {
  pegawai: PegawaiCache[]
  stats: DashboardStats
}

export default function KabupatenDashboard({ pegawai, stats }: Props) {
  const [search, setSearch] = useState('')
  const [filteredPegawai, setFilteredPegawai] = useState(pegawai)

  const handleSearch = (value: string) => {
    setSearch(value)
    if (!value.trim()) {
      setFilteredPegawai(pegawai)
      return
    }

    const filtered = pegawai.filter(p => 
      p.nama.toLowerCase().includes(value.toLowerCase()) ||
      p.nip.includes(value) ||
      p.unit_kerja.toLowerCase().includes(value.toLowerCase()) ||
      p.jabatan.toLowerCase().includes(value.toLowerCase())
    )
    setFilteredPegawai(filtered)
  }

  const handleCreatePengajuan = (nip: string) => {
    window.location.href = `/kabupaten/pengajuan/create/${nip}`
  }

  return (
    <AppLayout title="Dashboard Kabupaten">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard Kabupaten</h1>
          <p className="text-gray-600">Kelola pengajuan pensiun ASN di wilayah Anda</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Pegawai TMT Pensiun
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_pegawai_tmt}</div>
              <p className="text-xs text-muted-foreground">
                Pegawai yang TMT Pensiun ≤ hari ini
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Draft Pengajuan
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_pengajuan_draft}</div>
              <p className="text-xs text-muted-foreground">
                Pengajuan dalam status draft
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Menunggu Review
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_pengajuan_diajukan}</div>
              <p className="text-xs text-muted-foreground">
                Pengajuan menunggu persetujuan
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Disetujui
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_pengajuan_disetujui}</div>
              <p className="text-xs text-muted-foreground">
                Pengajuan yang telah disetujui
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Pegawai Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Data Pegawai TMT Pensiun</CardTitle>
                <CardDescription>
                  Daftar pegawai yang TMT Pensiun ≤ hari ini dan dapat mengajukan pensiun
                </CardDescription>
              </div>
            </div>
            
            {/* Search */}
            <div className="flex items-center space-x-2">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari pegawai..."
                  value={search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>NIP</TableHead>
                    <TableHead>Nama</TableHead>
                    <TableHead>Golongan</TableHead>
                    <TableHead>TMT Pensiun</TableHead>
                    <TableHead>Unit Kerja</TableHead>
                    <TableHead>Jabatan</TableHead>
                    <TableHead>Status Pengajuan</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPegawai.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                        {search ? 'Tidak ada pegawai yang ditemukan' : 'Tidak ada data pegawai'}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredPegawai.map((p) => (
                      <TableRow key={p.id}>
                        <TableCell className="font-mono">{p.nip}</TableCell>
                        <TableCell className="font-medium">{p.nama}</TableCell>
                        <TableCell>{p.golongan}</TableCell>
                        <TableCell>{formatDate(p.tmt_pensiun)}</TableCell>
                        <TableCell className="max-w-32 truncate">{p.unit_kerja}</TableCell>
                        <TableCell className="max-w-32 truncate">{p.jabatan}</TableCell>
                        <TableCell>
                          {p.has_pengajuan_aktif ? (
                            <Badge variant={getStatusBadgeVariant(p.pengajuan_status || 'Draft')}>
                              {getStatusLabel(p.pengajuan_status || 'Draft')}
                            </Badge>
                          ) : (
                            <Badge variant="outline">Belum Ada</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          {!p.has_pengajuan_aktif ? (
                            <Button
                              size="sm"
                              onClick={() => handleCreatePengajuan(p.nip)}
                              className="gap-2"
                            >
                              <Plus className="h-4 w-4" />
                              Buat Pengajuan
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.location.href = `/kabupaten/pengajuan/${p.nip}/edit`}
                            >
                              Lihat Detail
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
            
            {filteredPegawai.length > 0 && (
              <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                <div>
                  Menampilkan {filteredPegawai.length} dari {pegawai.length} pegawai
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
