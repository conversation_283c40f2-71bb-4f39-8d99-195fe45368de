import { Head } from '@inertiajs/react'
import { useState } from 'react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  FileBarChart, 
  Download, 
  Filter,
  Eye,
  FileText,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'
import { formatDate, getStatusBadgeVariant, getStatusLabel, getJenisPensiunLabel } from '@/lib/utils'
import { PengajuanPensiun } from '@/types'

interface Summary {
  total: number
  disetujui: number
  ditolak: number
  menunggu: number
}

interface Props {
  pengajuan_list: {
    data: PengajuanPensiun[]
    current_page: number
    last_page: number
    total: number
  }
  summary: Summary
  filters: {
    start_date?: string
    end_date?: string
    status?: string
    jenis_pensiun?: string
  }
  status_options: string[]
  jenis_pensiun_options: string[]
}

export default function LaporanKanwil({ 
  pengajuan_list, 
  summary, 
  filters, 
  status_options, 
  jenis_pensiun_options 
}: Props) {
  const [startDate, setStartDate] = useState(filters.start_date || '')
  const [endDate, setEndDate] = useState(filters.end_date || '')
  const [statusFilter, setStatusFilter] = useState(filters.status || 'all')
  const [jenisFilter, setJenisFilter] = useState(filters.jenis_pensiun || 'all')

  const handleFilterChange = () => {
    const params = new URLSearchParams()
    if (startDate) params.append('start_date', startDate)
    if (endDate) params.append('end_date', endDate)
    if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter)
    if (jenisFilter && jenisFilter !== 'all') params.append('jenis_pensiun', jenisFilter)
    
    window.location.href = `/kanwil/laporan?${params.toString()}`
  }

  const handleResetFilter = () => {
    setStartDate('')
    setEndDate('')
    setStatusFilter('all')
    setJenisFilter('all')
    window.location.href = '/kanwil/laporan'
  }

  const handleExport = () => {
    const params = new URLSearchParams()
    if (startDate) params.append('start_date', startDate)
    if (endDate) params.append('end_date', endDate)
    if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter)
    if (jenisFilter && jenisFilter !== 'all') params.append('jenis_pensiun', jenisFilter)
    params.append('export', 'excel')
    
    window.location.href = `/kanwil/laporan?${params.toString()}`
  }

  return (
    <>
      <Head title="Laporan Kanwil" />
      <AppLayout title="Laporan Kanwil">
        <div className="space-y-8 pb-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-slate-800 mb-2">Laporan Kanwil</h1>
            <p className="text-slate-600">Laporan komprehensif pengajuan pensiun</p>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Total</CardTitle>
                <div className="p-2 bg-blue-50 rounded-lg">
                  <FileText className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-800 mb-1">{summary.total}</div>
                <p className="text-xs text-slate-500">Pengajuan</p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Disetujui</CardTitle>
                <div className="p-2 bg-emerald-50 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-emerald-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-emerald-600 mb-1">{summary.disetujui}</div>
                <p className="text-xs text-slate-500">Pengajuan</p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Ditolak</CardTitle>
                <div className="p-2 bg-red-50 rounded-lg">
                  <XCircle className="h-4 w-4 text-red-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600 mb-1">{summary.ditolak}</div>
                <p className="text-xs text-slate-500">Pengajuan</p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Menunggu</CardTitle>
                <div className="p-2 bg-orange-50 rounded-lg">
                  <Clock className="h-4 w-4 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600 mb-1">{summary.menunggu}</div>
                <p className="text-xs text-slate-500">Pengajuan</p>
              </CardContent>
            </Card>
          </div>

          {/* Filter & Export */}
          <Card className="border-0 shadow-sm bg-white">
            <CardHeader className="border-b border-slate-100 bg-slate-50/50">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-slate-800">Filter Laporan</CardTitle>
                  <CardDescription className="text-slate-600">
                    Filter dan ekspor data pengajuan pensiun
                  </CardDescription>
                </div>
                <Button 
                  onClick={handleExport}
                  className="bg-emerald-600 hover:bg-emerald-700 text-white shadow-sm"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Excel
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 pt-4">
                <div className="space-y-2">
                  <Label htmlFor="start_date" className="text-slate-700 font-medium">Tanggal Mulai</Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="end_date" className="text-slate-700 font-medium">Tanggal Akhir</Label>
                  <Input
                    id="end_date"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label className="text-slate-700 font-medium">Status</Label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                      <SelectValue placeholder="Semua Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua Status</SelectItem>
                      {status_options.map((status) => (
                        <SelectItem key={status} value={status}>
                          {getStatusLabel(status)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-slate-700 font-medium">Jenis Pensiun</Label>
                  <Select value={jenisFilter} onValueChange={setJenisFilter}>
                    <SelectTrigger className="border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                      <SelectValue placeholder="Semua Jenis" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Semua Jenis</SelectItem>
                      {jenis_pensiun_options.map((jenis) => (
                        <SelectItem key={jenis} value={jenis}>
                          {getJenisPensiunLabel(jenis)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2 flex flex-col justify-end">
                  <div className="flex gap-2">
                    <Button 
                      onClick={handleFilterChange}
                      className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm flex-1"
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Filter
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={handleResetFilter}
                      className="border-slate-200 text-slate-600 hover:bg-slate-50"
                    >
                      Reset
                    </Button>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Data Table */}
          <Card className="border-0 shadow-sm bg-white">
            <CardHeader className="border-b border-slate-100 bg-slate-50/50">
              <CardTitle className="text-lg font-semibold text-slate-800">Data Pengajuan</CardTitle>
              <CardDescription className="text-slate-600">
                Menampilkan {pengajuan_list.data.length} dari {pengajuan_list.total} pengajuan
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="border-b border-slate-100 bg-slate-50/30">
                      <TableHead className="font-semibold text-slate-700">NIP</TableHead>
                      <TableHead className="font-semibold text-slate-700">Nama Pegawai</TableHead>
                      <TableHead className="font-semibold text-slate-700">Jenis Pensiun</TableHead>
                      <TableHead className="font-semibold text-slate-700">Tanggal Pengajuan</TableHead>
                      <TableHead className="font-semibold text-slate-700">Status</TableHead>
                      <TableHead className="font-semibold text-slate-700">Operator</TableHead>
                      <TableHead className="text-right font-semibold text-slate-700">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pengajuan_list.data.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-12 text-slate-500">
                          <div className="flex flex-col items-center gap-2">
                            <FileBarChart className="h-8 w-8 text-slate-300" />
                            <span>Tidak ada data pengajuan</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      pengajuan_list.data.map((pengajuan) => (
                        <TableRow key={pengajuan.id} className="border-b border-slate-50 hover:bg-slate-50/50 transition-colors">
                          <TableCell className="font-mono text-slate-600 py-4">{pengajuan.nip}</TableCell>
                          <TableCell className="font-medium text-slate-800 py-4">
                            {pengajuan.pegawai?.nama || 'N/A'}
                          </TableCell>
                          <TableCell className="text-slate-600 py-4">{getJenisPensiunLabel(pengajuan.jenis_pensiun)}</TableCell>
                          <TableCell className="text-slate-600 py-4">{formatDate(pengajuan.created_at)}</TableCell>
                          <TableCell className="py-4">
                            <Badge variant={getStatusBadgeVariant(pengajuan.status)} className="font-medium">
                              {getStatusLabel(pengajuan.status)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-sm text-slate-500 py-4">
                            {pengajuan.user?.name || 'N/A'}
                          </TableCell>
                          <TableCell className="text-right py-4">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.location.href = `/kanwil/pengajuan/${pengajuan.id}`}
                              className="gap-1 border-slate-200 text-slate-600 hover:bg-slate-50 hover:text-slate-800"
                            >
                              <Eye className="h-4 w-4" />
                              Detail
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    </>
  )
}
