<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PengajuanController;
use App\Http\Controllers\ApprovalController;
use App\Http\Controllers\FileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return inertia('Welcome');
});

// Auth routes (guest only)
Route::middleware('guest')->group(function () {
    Route::get('/login', function () {
        return inertia('Auth/Login');
    })->name('login');
    
    Route::get('/forgot-password', function () {
        return inertia('Auth/ForgotPassword');
    })->name('password.request');
});

Route::middleware(['auth:sanctum', config('fortify.guard')])->group(function () {
    // Dashboard utama - redirect berdasarkan role
    Route::get('/dashboard', function () {
        $user = auth()->user();
        
        return match($user->role) {
            'Kanwil' => redirect()->route('kanwil.dashboard'),
            'Kabupaten' => redirect()->route('kabupaten.dashboard'),
            'Admin Pusat' => redirect()->route('admin-pusat.dashboard'),
            default => inertia('Dashboard')
        };
    })->name('dashboard');
    
    // File handling routes
    Route::prefix('file')->name('file.')->group(function () {
        Route::get('/download/{dokumen}', [FileController::class, 'download'])->name('download');
        Route::get('/preview/{dokumen}', [FileController::class, 'preview'])->name('preview');
        Route::get('/info/{dokumen}', [FileController::class, 'info'])->name('info');
        Route::delete('/delete/{dokumen}', [FileController::class, 'delete'])->name('delete');
        Route::post('/batch-upload', [FileController::class, 'batchUpload'])->name('batch-upload');
        Route::get('/storage-stats', [FileController::class, 'storageStats'])->name('storage-stats');
    });
    
    // Dashboard Kabupaten (Operator) - Kanwil juga bisa akses
    Route::middleware('role:Kabupaten,Kanwil')->prefix('kabupaten')->name('kabupaten.')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'kabupaten'])->name('dashboard');

        // Pegawai routes
        Route::get('/pegawai', [PegawaiController::class, 'index'])->name('pegawai.index');
        Route::get('/pegawai/{nip}', [PegawaiController::class, 'show'])->name('pegawai.show');

        // Pengajuan pensiun routes
        Route::get('/pengajuan', [PengajuanController::class, 'index'])->name('pengajuan.index');
        Route::get('/pengajuan/create', [PengajuanController::class, 'createSelect'])->name('pengajuan.create');
        Route::get('/pengajuan/create/{nip}', [PengajuanController::class, 'create'])->name('pengajuan.create.nip');
        Route::post('/pengajuan', [PengajuanController::class, 'store'])->name('pengajuan.store');
        Route::get('/pengajuan/{id}', [PengajuanController::class, 'show'])->name('pengajuan.show');
        Route::get('/pengajuan/{id}/edit', [PengajuanController::class, 'edit'])->name('pengajuan.edit');
        Route::put('/pengajuan/{id}', [PengajuanController::class, 'update'])->name('pengajuan.update');
        Route::post('/pengajuan/{id}/submit', [PengajuanController::class, 'submit'])->name('pengajuan.submit');
    });
    
    // Dashboard Kanwil (Superadmin)
    Route::middleware('role:Kanwil')->prefix('kanwil')->name('kanwil.')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'kanwil'])->name('dashboard');
        Route::get('/statistik', [DashboardController::class, 'statistikKanwil'])->name('statistik');
        Route::get('/laporan', [DashboardController::class, 'laporanKanwil'])->name('laporan');

        // Pegawai management (Kanwil superadmin)
        Route::get('/pegawai', [PegawaiController::class, 'indexKanwil'])->name('pegawai.index');
        Route::get('/pegawai/{nip}', [PegawaiController::class, 'showKanwil'])->name('pegawai.show');
        Route::post('/pegawai/sync', [PegawaiController::class, 'sync'])->name('pegawai.sync');

        // Pengajuan management (Kanwil superadmin)
        Route::get('/pengajuan-kelola', [PengajuanController::class, 'indexKanwil'])->name('pengajuan-kelola.index');
        Route::get('/pengajuan/create', [PengajuanController::class, 'createSelectKanwil'])->name('pengajuan.create');
        Route::get('/pengajuan/create/{nip}', [PengajuanController::class, 'createKanwil'])->name('pengajuan.create.nip');
        Route::post('/pengajuan', [PengajuanController::class, 'storeKanwil'])->name('pengajuan.store');
        Route::get('/pengajuan-kelola/{id}', [PengajuanController::class, 'showKanwil'])->name('pengajuan-kelola.show');
        Route::get('/pengajuan-kelola/{id}/edit', [PengajuanController::class, 'editKanwil'])->name('pengajuan-kelola.edit');
        Route::put('/pengajuan-kelola/{id}', [PengajuanController::class, 'updateKanwil'])->name('pengajuan-kelola.update');
        Route::delete('/pengajuan-kelola/{id}', [PengajuanController::class, 'destroyKanwil'])->name('pengajuan-kelola.destroy');

        // Approval routes
        Route::get('/pengajuan', [ApprovalController::class, 'index'])->name('pengajuan.index');
        Route::get('/pengajuan/{id}', [ApprovalController::class, 'show'])->name('pengajuan.detail');
        Route::post('/pengajuan/{id}/approve', [ApprovalController::class, 'approve'])->name('pengajuan.approve');
        Route::post('/pengajuan/{id}/reject', [ApprovalController::class, 'reject'])->name('pengajuan.reject');
    });
    
    // Dashboard Admin Pusat (Viewer)
    Route::middleware('role:Admin Pusat')->prefix('admin-pusat')->name('admin-pusat.')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'adminPusat'])->name('dashboard');
        Route::get('/statistik', [DashboardController::class, 'statistik'])->name('statistik');
        
        // View only pengajuan detail
        Route::get('/pengajuan/{id}', [ApprovalController::class, 'show'])->name('pengajuan.detail');
    });
});
