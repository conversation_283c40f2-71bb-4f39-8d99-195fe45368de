<?php

namespace App\Http\Controllers;

use App\Models\PegawaiCache;
use App\Models\PengajuanPensiun;
use App\Services\PegawaiService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    public function __construct(
        private PegawaiService $pegawaiService
    ) {}

    /**
     * Dashboard Kabupaten (Operator)
     */
    public function kabupaten(): Response
    {
        $user = auth()->user();
        
        // Ambil pegawai yang sudah pensiun (untuk dibuatkan pengajuan berkas)
        $pegawaiSudahPensiun = $this->pegawaiService->getPegawaiSudahPensiun();

        // Add status pengajuan untuk setiap pegawai
        $pegawaiWithStatus = $pegawaiSudahPensiun->map(function ($pegawai) {
            $pengajuanAktif = $pegawai->pengajuanPensiun()
                ->whereIn('status', ['Draft', 'Diajukan'])
                ->first();

            return [
                ...$pegawai->toArray(),
                'has_pengajuan_aktif' => !is_null($pengajuanAktif),
                'pengajuan_status' => $pengajuanAktif?->status,
            ];
        });

        // Statistik dashboard untuk Kabupaten
        $stats = [
            'total_pegawai_sudah_pensiun' => $pegawaiSudahPensiun->count(),
            'total_pengajuan_draft' => PengajuanPensiun::where('user_id', $user->id)->where('status', 'Draft')->count(),
            'total_pengajuan_diajukan' => PengajuanPensiun::where('user_id', $user->id)->where('status', 'Diajukan')->count(),
            'total_pengajuan_disetujui' => PengajuanPensiun::where('user_id', $user->id)->where('status', 'Disetujui')->count(),
        ];

        return Inertia::render('Dashboard/Kabupaten', [
            'pegawai' => $pegawaiWithStatus,
            'stats' => $stats
        ]);
    }

    /**
     * Dashboard Kanwil (Superadmin)
     */
    public function kanwil(Request $request): Response
    {
        $query = PengajuanPensiun::with(['pegawai', 'user', 'penyetuju'])
            ->latest();

        // Filter berdasarkan status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter berdasarkan jenis pensiun
        if ($request->jenis_pensiun) {
            $query->where('jenis_pensiun', $request->jenis_pensiun);
        }

        $pengajuanList = $query->paginate(15);

        // Statistik
        $statistik = [
            'total_pengajuan' => PengajuanPensiun::count(),
            'menunggu_review' => PengajuanPensiun::where('status', 'Diajukan')->count(),
            'disetujui_bulan_ini' => PengajuanPensiun::where('status', 'Disetujui')
                ->whereMonth('tanggal_disetujui', now()->month)
                ->count(),
            'ditolak_bulan_ini' => PengajuanPensiun::where('status', 'Ditolak')
                ->whereMonth('tanggal_disetujui', now()->month)
                ->count(),
        ];

        // Statistik per jenis pensiun
        $statistikJenis = PengajuanPensiun::selectRaw('jenis_pensiun, status, COUNT(*) as total')
            ->groupBy('jenis_pensiun', 'status')
            ->get()
            ->groupBy('jenis_pensiun');

        // Cache stats
        $cacheStats = $this->pegawaiService->getCacheStats();

        return Inertia::render('Dashboard/Kanwil', [
            'pengajuan_list' => $pengajuanList,
            'statistik' => $statistik,
            'statistik_jenis' => $statistikJenis,
            'cache_stats' => $cacheStats,
            'filters' => $request->only(['status', 'jenis_pensiun']),
            'status_options' => ['Draft', 'Diajukan', 'Disetujui', 'Ditolak'],
            'jenis_pensiun_options' => ['BUP', 'Sakit', 'Janda/Duda', 'APS']
        ]);
    }

    /**
     * Dashboard Admin Pusat (Viewer)
     */
    public function adminPusat(Request $request): Response
    {
        $query = PengajuanPensiun::with(['pegawai', 'user', 'penyetuju'])
            ->latest();

        // Filter berdasarkan status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter berdasarkan jenis pensiun
        if ($request->jenis_pensiun) {
            $query->where('jenis_pensiun', $request->jenis_pensiun);
        }

        // Filter berdasarkan periode
        if ($request->periode) {
            switch ($request->periode) {
                case 'bulan_ini':
                    $query->whereMonth('created_at', now()->month);
                    break;
                case '3_bulan':
                    $query->where('created_at', '>=', now()->subMonths(3));
                    break;
                case '6_bulan':
                    $query->where('created_at', '>=', now()->subMonths(6));
                    break;
                case 'tahun_ini':
                    $query->whereYear('created_at', now()->year);
                    break;
            }
        }

        $pengajuanList = $query->paginate(20);

        // Statistik komprehensif
        $statistik = [
            'total_pengajuan' => PengajuanPensiun::count(),
            'total_disetujui' => PengajuanPensiun::where('status', 'Disetujui')->count(),
            'total_ditolak' => PengajuanPensiun::where('status', 'Ditolak')->count(),
            'menunggu_review' => PengajuanPensiun::where('status', 'Diajukan')->count(),
            
            'bulan_ini' => PengajuanPensiun::whereMonth('created_at', now()->month)->count(),
            'tahun_ini' => PengajuanPensiun::whereYear('created_at', now()->year)->count(),
            
            'rata_waktu_approval' => PengajuanPensiun::whereNotNull('tanggal_disetujui')
                ->selectRaw('AVG(DATEDIFF(tanggal_disetujui, created_at)) as avg_days')
                ->value('avg_days'),
        ];

        // Statistik per bulan (12 bulan terakhir)
        $statistikBulanan = PengajuanPensiun::selectRaw('
                YEAR(created_at) as tahun, 
                MONTH(created_at) as bulan, 
                status,
                COUNT(*) as total
            ')
            ->where('created_at', '>=', now()->subMonths(12))
            ->groupBy('tahun', 'bulan', 'status')
            ->orderBy('tahun', 'desc')
            ->orderBy('bulan', 'desc')
            ->get()
            ->groupBy(function ($item) {
                return $item->tahun . '-' . str_pad($item->bulan, 2, '0', STR_PAD_LEFT);
            });

        // Statistik per jenis pensiun
        $statistikJenis = PengajuanPensiun::selectRaw('jenis_pensiun, status, COUNT(*) as total')
            ->groupBy('jenis_pensiun', 'status')
            ->get()
            ->groupBy('jenis_pensiun');

        // Cache stats
        $cacheStats = $this->pegawaiService->getCacheStats();

        return Inertia::render('Dashboard/AdminPusat', [
            'pengajuan_list' => $pengajuanList,
            'statistik' => $statistik,
            'statistik_bulanan' => $statistikBulanan,
            'statistik_jenis' => $statistikJenis,
            'cache_stats' => $cacheStats,
            'filters' => $request->only(['status', 'jenis_pensiun', 'periode']),
            'status_options' => ['Draft', 'Diajukan', 'Disetujui', 'Ditolak'],
            'jenis_pensiun_options' => ['BUP', 'Sakit', 'Janda/Duda', 'APS'],
            'periode_options' => [
                'bulan_ini' => 'Bulan Ini',
                '3_bulan' => '3 Bulan Terakhir',
                '6_bulan' => '6 Bulan Terakhir',
                'tahun_ini' => 'Tahun Ini'
            ]
        ]);
    }

    /**
     * Statistik detail untuk Admin Pusat
     */
    public function statistik(): Response
    {
        // Statistik menyeluruh
        $totalPegawai = PegawaiCache::count();
        $pegawaiTmtTotal = PegawaiCache::tmtPensiunHariIni()->count();
        
        // Tingkat adoption (pegawai yang sudah diajukan vs yang TMT)
        $tingkatAdopsi = $pegawaiTmtTotal > 0 
            ? (PengajuanPensiun::distinct('nip')->count() / $pegawaiTmtTotal) * 100 
            : 0;

        // Performance metrics
        $rataWaktuApproval = PengajuanPensiun::whereNotNull('tanggal_disetujui')
            ->selectRaw('AVG(DATEDIFF(tanggal_disetujui, created_at)) as avg_days')
            ->value('avg_days') ?? 0;

        $tingkatApproval = PengajuanPensiun::where('status', '!=', 'Draft')->count() > 0
            ? (PengajuanPensiun::where('status', 'Disetujui')->count() / 
               PengajuanPensiun::where('status', '!=', 'Draft')->count()) * 100
            : 0;

        return Inertia::render('AdminPusat/Statistik', [
            'total_pegawai' => $totalPegawai,
            'pegawai_tmt_total' => $pegawaiTmtTotal,
            'tingkat_adopsi' => round($tingkatAdopsi, 2),
            'rata_waktu_approval' => round($rataWaktuApproval, 1),
            'tingkat_approval' => round($tingkatApproval, 2),
            'cache_stats' => $this->pegawaiService->getCacheStats()
        ]);
    }

    /**
     * Statistik Kanwil
     */
    public function statistikKanwil(Request $request): Response
    {
        // Statistik komprehensif untuk Kanwil
        $statistik = [
            'total_pengajuan' => PengajuanPensiun::count(),
            'total_disetujui' => PengajuanPensiun::where('status', 'Disetujui')->count(),
            'total_ditolak' => PengajuanPensiun::where('status', 'Ditolak')->count(),
            'menunggu_review' => PengajuanPensiun::where('status', 'Diajukan')->count(),
            'bulan_ini' => PengajuanPensiun::whereMonth('created_at', now()->month)->count(),
        ];

        // Statistik per jenis pensiun
        $statistikJenis = PengajuanPensiun::select('jenis_pensiun', 'status')
            ->selectRaw('COUNT(*) as total')
            ->groupBy('jenis_pensiun', 'status')
            ->get()
            ->groupBy('jenis_pensiun');

        // Statistik per bulan (6 bulan terakhir)
        $statistikBulanan = PengajuanPensiun::select(
                DB::raw('MONTH(created_at) as bulan'),
                DB::raw('YEAR(created_at) as tahun'),
                DB::raw('COUNT(*) as total')
            )
            ->where('created_at', '>=', now()->subMonths(6))
            ->groupBy('bulan', 'tahun')
            ->orderBy('tahun', 'desc')
            ->orderBy('bulan', 'desc')
            ->get();

        // Cache stats
        $cacheStats = $this->pegawaiService->getCacheStats();

        return Inertia::render('Kanwil/Statistik', [
            'statistik' => $statistik,
            'statistik_jenis' => $statistikJenis,
            'statistik_bulanan' => $statistikBulanan,
            'cache_stats' => $cacheStats
        ]);
    }

    /**
     * Laporan Kanwil
     */
    public function laporanKanwil(Request $request): Response
    {
        $query = PengajuanPensiun::with(['pegawai', 'user', 'penyetuju'])
            ->latest();

        // Filter berdasarkan tanggal
        if ($request->start_date && $request->end_date) {
            $query->whereBetween('created_at', [
                $request->start_date . ' 00:00:00',
                $request->end_date . ' 23:59:59'
            ]);
        }

        // Filter berdasarkan status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter berdasarkan jenis pensiun
        if ($request->jenis_pensiun) {
            $query->where('jenis_pensiun', $request->jenis_pensiun);
        }

        $pengajuanList = $query->paginate(50);

        // Summary data
        $summary = [
            'total' => $query->count(),
            'disetujui' => $query->clone()->where('status', 'Disetujui')->count(),
            'ditolak' => $query->clone()->where('status', 'Ditolak')->count(),
            'menunggu' => $query->clone()->where('status', 'Diajukan')->count(),
        ];

        return Inertia::render('Kanwil/Laporan', [
            'pengajuan_list' => $pengajuanList,
            'summary' => $summary,
            'filters' => $request->only(['start_date', 'end_date', 'status', 'jenis_pensiun']),
            'status_options' => ['Draft', 'Diajukan', 'Disetujui', 'Ditolak'],
            'jenis_pensiun_options' => ['BUP', 'Sakit', 'Janda/Duda', 'APS']
        ]);
    }
}