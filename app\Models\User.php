<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Http\UploadedFile;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Storage;
use Laravel\Fortify\TwoFactorAuthenticatable;

class User extends Authenticatable implements MustVerifyEmail
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, TwoFactorAuthenticatable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',

    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Update the user's profile photo.
     */
    public function updateProfilePhoto(UploadedFile $photo): void
    {
        tap($this->profile_photo_path, function ($previous) use ($photo) {
            $this->forceFill([
                'profile_photo_path' => $photo->storePublicly(
                    'profile-photos',
                    ['disk' => 'public']
                ),
            ])->save();

            if ($previous) {
                Storage::disk('public')->delete($previous);
            }
        });
    }

    /**
     * Get the URL to the user's profile photo.
     */
    public function getProfilePhotoUrlAttribute(): string
    {
        return $this->profile_photo_path
            ? asset('storage/'.$this->profile_photo_path)
            : '';
    }

    // Relationship dengan PengajuanPensiun (yang dibuat)
    public function pengajuanPensiun()
    {
        return $this->hasMany(PengajuanPensiun::class);
    }

    // Relationship dengan PengajuanPensiun (yang disetujui)
    public function pengajuanDisetujui()
    {
        return $this->hasMany(PengajuanPensiun::class, 'disetujui_oleh');
    }

    // Relationship dengan LogAktivitas
    public function logAktivitas()
    {
        return $this->hasMany(LogAktivitas::class);
    }

    // Check role methods
    public function isKanwil()
    {
        return $this->role === 'Kanwil';
    }

    public function isKabupaten()
    {
        return $this->role === 'Kabupaten';
    }

    public function isAdminPusat()
    {
        return $this->role === 'Admin Pusat';
    }

    // Scope untuk role tertentu
    public function scopeRole($query, $role)
    {
        return $query->where('role', $role);
    }

    // Scope untuk kantor tertentu
    public function scopeKantor($query, $kantorId)
    {
        return $query->where('kantor_id', $kantorId);
    }
}
