import { Head, useForm, router } from '@inertiajs/react'
import { useState } from 'react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { 
  CheckCircle, 
  XCircle, 
  FileText, 
  Download, 
  Eye, 
  Calendar,
  User,
  Building,
  Award,
  Clock,
  AlertTriangle
} from 'lucide-react'
import { PengajuanPensiun, Dokumen } from '@/types'

interface Props {
  pengajuan: PengajuanPensiun
  dokumen_wajib: string[]
  can_approve: boolean
  log_aktivitas: any[]
}

export default function PengajuanDetail({ pengajuan, dokumen_wajib, can_approve, log_aktivitas }: Props) {
  const [showRejectDialog, setShowRejectDialog] = useState(false)
  const [rejectReason, setRejectReason] = useState('')

  const { post, processing } = useForm()

  const handleApprove = () => {
    post(route('kanwil.pengajuan.approve', pengajuan.id), {
      onSuccess: () => {
        // Success handled by redirect
      }
    })
  }

  const handleReject = () => {
    if (!rejectReason.trim()) {
      alert('Harap isi alasan penolakan')
      return
    }

    router.post(route('kanwil.pengajuan.reject', pengajuan.id), {
      catatan_penolakan: rejectReason
    }, {
      onSuccess: () => {
        setShowRejectDialog(false)
        setRejectReason('')
      }
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Draft':
        return <Badge variant="secondary">Draft</Badge>
      case 'Diajukan':
        return <Badge variant="default">Diajukan</Badge>
      case 'Disetujui':
        return <Badge variant="default" className="bg-green-100 text-green-800">Disetujui</Badge>
      case 'Ditolak':
        return <Badge variant="destructive">Ditolak</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Disetujui':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'Ditolak':
        return <XCircle className="h-5 w-5 text-red-600" />
      default:
        return <Clock className="h-5 w-5 text-blue-600" />
    }
  }

  return (
    <AppLayout title={`Detail Pengajuan - ${pengajuan.pegawai?.nama}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Detail Pengajuan Pensiun</h1>
            <p className="text-gray-600">Review pengajuan pensiun untuk approval</p>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(pengajuan.status)}
            {getStatusIcon(pengajuan.status)}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Data Pegawai */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Data Pegawai
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">NIP</Label>
                  <p className="text-lg font-semibold">{pengajuan.pegawai?.nip}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Nama</Label>
                  <p className="text-lg font-semibold">{pengajuan.pegawai?.nama}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Golongan</Label>
                  <p className="text-lg">{pengajuan.pegawai?.golongan}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Jabatan</Label>
                  <p className="text-lg">{pengajuan.pegawai?.jabatan}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">Unit Kerja</Label>
                  <p className="text-lg">{pengajuan.pegawai?.unit_kerja}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">TMT Pensiun</Label>
                  <p className="text-lg">{pengajuan.pegawai?.tmt_pensiun ? new Date(pengajuan.pegawai.tmt_pensiun).toLocaleDateString('id-ID') : '-'}</p>
                </div>
              </CardContent>
            </Card>

            {/* Data Pengajuan */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Data Pengajuan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Jenis Pensiun</Label>
                    <p className="text-lg font-semibold">{pengajuan.jenis_pensiun}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Status</Label>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(pengajuan.status)}
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Tanggal Pengajuan</Label>
                    <p className="text-lg">{new Date(pengajuan.created_at).toLocaleDateString('id-ID')}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Diajukan Oleh</Label>
                    <p className="text-lg">{pengajuan.user?.name}</p>
                  </div>
                </div>

                {pengajuan.catatan_penolakan && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Alasan Penolakan</Label>
                    <Alert className="mt-2">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>{pengajuan.catatan_penolakan}</AlertDescription>
                    </Alert>
                  </div>
                )}

                {pengajuan.tanggal_disetujui && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Tanggal Disetujui/Ditolak</Label>
                    <p className="text-lg">{new Date(pengajuan.tanggal_disetujui).toLocaleDateString('id-ID')}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Dokumen */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Dokumen Pendukung
                </CardTitle>
                <CardDescription>
                  Dokumen yang diupload untuk pengajuan ini
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {pengajuan.dokumen?.map((dokumen: Dokumen) => (
                    <div key={dokumen.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-blue-600" />
                        <div>
                          <p className="font-medium">{dokumen.jenis_dokumen}</p>
                          <p className="text-sm text-gray-500">{dokumen.nama_file}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(route('file.preview', dokumen.id), '_blank')}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(route('file.download', dokumen.id), '_blank')}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Approval Actions */}
            {can_approve && pengajuan.status === 'Diajukan' && (
              <Card>
                <CardHeader>
                  <CardTitle>Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    onClick={handleApprove}
                    disabled={processing}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Setujui Pengajuan
                  </Button>

                  <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
                    <DialogTrigger asChild>
                      <Button
                        variant="destructive"
                        className="w-full"
                        disabled={processing}
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        Tolak Pengajuan
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Tolak Pengajuan</DialogTitle>
                        <DialogDescription>
                          Berikan alasan penolakan untuk pengajuan ini
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-3">
                        <Label htmlFor="reject-reason">Alasan Penolakan</Label>
                        <Textarea
                          id="reject-reason"
                          value={rejectReason}
                          onChange={(e) => setRejectReason(e.target.value)}
                          placeholder="Masukkan alasan penolakan..."
                          rows={4}
                        />
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setShowRejectDialog(false)}
                        >
                          Batal
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleReject}
                          disabled={processing || !rejectReason.trim()}
                        >
                          Tolak Pengajuan
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </CardContent>
              </Card>
            )}

            {/* Log Aktivitas */}
            <Card>
              <CardHeader>
                <CardTitle>Log Aktivitas</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {log_aktivitas.map((log, index) => (
                    <div key={index} className="flex items-start gap-3 p-2 border rounded">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{log.aktivitas}</p>
                        <p className="text-xs text-gray-500">
                          {log.user?.name} • {new Date(log.created_at).toLocaleString('id-ID')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  )
}
