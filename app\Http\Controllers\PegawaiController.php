<?php

namespace App\Http\Controllers;

use App\Models\PegawaiCache;
use App\Services\PegawaiService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PegawaiController extends Controller
{
    public function __construct(
        private PegawaiService $pegawaiService
    ) {}

    /**
     * Display a listing of pegawai
     */
    public function index(Request $request): Response
    {
        $query = PegawaiCache::query();

        // Search functionality
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->search . '%')
                  ->orWhere('nip', 'like', '%' . $request->search . '%')
                  ->orWhere('jabatan', 'like', '%' . $request->search . '%')
                  ->orWhere('unit_kerja', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by unit kerja
        if ($request->unit_kerja) {
            $query->where('unit_kerja', 'like', '%' . $request->unit_kerja . '%');
        }

        // Filter by status kepegawaian
        if ($request->status_kepegawaian) {
            $query->where('status_kepegawaian', $request->status_kepegawaian);
        }

        // Filter by TMT pensiun (upcoming retirements)
        if ($request->tmt_filter) {
            switch ($request->tmt_filter) {
                case '1_bulan':
                    $query->whereBetween('tmt_pensiun', [now(), now()->addMonth()]);
                    break;
                case '3_bulan':
                    $query->whereBetween('tmt_pensiun', [now(), now()->addMonths(3)]);
                    break;
                case '6_bulan':
                    $query->whereBetween('tmt_pensiun', [now(), now()->addMonths(6)]);
                    break;
                case '1_tahun':
                    $query->whereBetween('tmt_pensiun', [now(), now()->addYear()]);
                    break;
            }
        }

        $pegawaiList = $query->orderBy('nama')->paginate(20);

        // Get unique unit kerja for filter
        $unitKerjaOptions = PegawaiCache::select('unit_kerja')
            ->distinct()
            ->whereNotNull('unit_kerja')
            ->orderBy('unit_kerja')
            ->pluck('unit_kerja');

        // Get cache stats
        $cacheStats = $this->pegawaiService->getCacheStats();

        return Inertia::render('Kabupaten/Pegawai/Index', [
            'pegawai_list' => $pegawaiList,
            'unit_kerja_options' => $unitKerjaOptions,
            'cache_stats' => $cacheStats,
            'filters' => $request->only(['search', 'unit_kerja', 'status_kepegawaian', 'tmt_filter']),
            'status_kepegawaian_options' => ['PNS', 'PPPK', 'Honorer'],
            'tmt_filter_options' => [
                '1_bulan' => '1 Bulan ke depan',
                '3_bulan' => '3 Bulan ke depan', 
                '6_bulan' => '6 Bulan ke depan',
                '1_tahun' => '1 Tahun ke depan'
            ]
        ]);
    }

    /**
     * Display the specified pegawai
     */
    public function show(string $nip): Response
    {
        $pegawai = PegawaiCache::where('nip', $nip)->firstOrFail();

        // Get pengajuan history for this pegawai
        $pengajuanHistory = $pegawai->pengajuanPensiun()
            ->with(['user', 'penyetuju'])
            ->latest()
            ->get();

        return Inertia::render('Kabupaten/Pegawai/Show', [
            'pegawai' => $pegawai,
            'pengajuan_history' => $pengajuanHistory,
            'can_create_pengajuan' => $this->canCreatePengajuan($pegawai)
        ]);
    }

    /**
     * Check if pengajuan can be created for this pegawai
     */
    private function canCreatePengajuan(PegawaiCache $pegawai): bool
    {
        // Check if there's already an active pengajuan
        $activePengajuan = $pegawai->pengajuanPensiun()
            ->whereIn('status', ['Draft', 'Diajukan'])
            ->exists();

        return !$activePengajuan;
    }

    /**
     * Display a listing of pegawai for Kanwil (superadmin)
     */
    public function indexKanwil(Request $request): Response
    {
        $query = PegawaiCache::query();

        // Search functionality
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->search . '%')
                  ->orWhere('nip', 'like', '%' . $request->search . '%')
                  ->orWhere('jabatan', 'like', '%' . $request->search . '%')
                  ->orWhere('unit_kerja', 'like', '%' . $request->search . '%');
            });
        }

        // Filter by unit kerja
        if ($request->unit_kerja) {
            $query->where('unit_kerja', 'like', '%' . $request->unit_kerja . '%');
        }

        // Filter by status kepegawaian
        if ($request->status_kepegawaian) {
            $query->where('status_kepegawaian', $request->status_kepegawaian);
        }

        // Filter by TMT pensiun (upcoming retirements)
        if ($request->tmt_filter) {
            switch ($request->tmt_filter) {
                case '1_bulan':
                    $query->whereBetween('tmt_pensiun', [now(), now()->addMonth()]);
                    break;
                case '3_bulan':
                    $query->whereBetween('tmt_pensiun', [now(), now()->addMonths(3)]);
                    break;
                case '6_bulan':
                    $query->whereBetween('tmt_pensiun', [now(), now()->addMonths(6)]);
                    break;
                case '1_tahun':
                    $query->whereBetween('tmt_pensiun', [now(), now()->addYear()]);
                    break;
            }
        }

        $pegawaiList = $query->orderBy('nama')->paginate(20);

        // Get unique unit kerja for filter
        $unitKerjaOptions = PegawaiCache::select('unit_kerja')
            ->distinct()
            ->whereNotNull('unit_kerja')
            ->orderBy('unit_kerja')
            ->pluck('unit_kerja');

        // Get cache stats
        $cacheStats = $this->pegawaiService->getCacheStats();

        return Inertia::render('Kanwil/Pegawai/Index', [
            'pegawai_list' => $pegawaiList,
            'unit_kerja_options' => $unitKerjaOptions,
            'cache_stats' => $cacheStats,
            'filters' => $request->only(['search', 'unit_kerja', 'status_kepegawaian', 'tmt_filter']),
            'status_kepegawaian_options' => ['PNS', 'PPPK', 'Honorer'],
            'tmt_filter_options' => [
                '1_bulan' => '1 Bulan ke depan',
                '3_bulan' => '3 Bulan ke depan',
                '6_bulan' => '6 Bulan ke depan',
                '1_tahun' => '1 Tahun ke depan'
            ]
        ]);
    }

    /**
     * Display the specified pegawai for Kanwil
     */
    public function showKanwil(string $nip): Response
    {
        $pegawai = PegawaiCache::where('nip', $nip)->firstOrFail();

        // Get pengajuan history for this pegawai
        $pengajuanHistory = $pegawai->pengajuanPensiun()
            ->with(['user', 'penyetuju'])
            ->latest()
            ->get();

        return Inertia::render('Kanwil/Pegawai/Show', [
            'pegawai' => $pegawai,
            'pengajuan_history' => $pengajuanHistory,
            'can_create_pengajuan' => $this->canCreatePengajuan($pegawai)
        ]);
    }

    /**
     * Sync pegawai data from SIMPEG
     */
    public function sync(Request $request)
    {
        try {
            $result = $this->pegawaiService->syncPegawaiData();

            return response()->json([
                'success' => true,
                'message' => 'Data pegawai berhasil disinkronisasi',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal sinkronisasi data: ' . $e->getMessage()
            ], 500);
        }
    }
}
