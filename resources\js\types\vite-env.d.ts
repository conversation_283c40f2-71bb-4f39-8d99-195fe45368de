/// <reference types="vite/client" />
/// <reference types="react" />
/// <reference types="react-dom" />

import { AxiosStatic } from 'axios';

// Global route function from <PERSON>iggy
declare global {
  function route(name?: string, params?: any, absolute?: boolean): string;

  interface Window {
    route: typeof route;
    axios: AxiosStatic;
  }
}

export {};

interface ImportMetaEnv {
  readonly VITE_APP_NAME: string
  readonly VITE_APP_ENV: string
  readonly VITE_APP_DEBUG: string
  readonly VITE_APP_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
  readonly glob: (pattern: string) => Record<string, () => Promise<any>>
}