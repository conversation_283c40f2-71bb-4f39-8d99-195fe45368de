<?php

namespace App\Http\Controllers;

use App\Models\Dokumen;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;

class FileController extends Controller
{
    public function __construct(
        private FileUploadService $fileUploadService
    ) {}

    /**
     * Download dokumen
     */
    public function download(int $dokumenId): Response
    {
        $dokumen = Dokumen::with('pengajuan')->findOrFail($dokumenId);
        
        // Check permission based on role
        $user = auth()->user();
        if (!$this->canAccessDokumen($user, $dokumen)) {
            abort(403, 'Anda tidak memiliki akses ke dokumen ini');
        }

        if (!Storage::disk('public')->exists($dokumen->path_file)) {
            abort(404, 'File tidak ditemukan');
        }

        $filePath = Storage::disk('public')->path($dokumen->path_file);
        
        return response()->download($filePath, $dokumen->nama_file, [
            'Content-Type' => $dokumen->mime_type,
        ]);
    }

    /**
     * View/preview dokumen (untuk file yang bisa di-preview)
     */
    public function preview(int $dokumenId): Response
    {
        $dokumen = Dokumen::with('pengajuan')->findOrFail($dokumenId);
        
        // Check permission
        $user = auth()->user();
        if (!$this->canAccessDokumen($user, $dokumen)) {
            abort(403, 'Anda tidak memiliki akses ke dokumen ini');
        }

        if (!Storage::disk('public')->exists($dokumen->path_file)) {
            abort(404, 'File tidak ditemukan');
        }

        $filePath = Storage::disk('public')->path($dokumen->path_file);
        $fileContent = file_get_contents($filePath);
        
        return response($fileContent, 200, [
            'Content-Type' => $dokumen->mime_type,
            'Content-Disposition' => 'inline; filename="' . $dokumen->nama_file . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ]);
    }

    /**
     * Get file info (untuk preview modal)
     */
    public function info(int $dokumenId): array
    {
        $dokumen = Dokumen::with('pengajuan')->findOrFail($dokumenId);
        
        // Check permission
        $user = auth()->user();
        if (!$this->canAccessDokumen($user, $dokumen)) {
            abort(403, 'Anda tidak memiliki akses ke dokumen ini');
        }

        $fileInfo = $this->fileUploadService->getFileInfo($dokumen->path_file);
        
        return [
            'dokumen' => $dokumen,
            'file_info' => $fileInfo,
            'is_image' => $this->fileUploadService->isImage($dokumen->mime_type),
            'is_document' => $this->fileUploadService->isDocument($dokumen->mime_type),
            'icon' => $this->fileUploadService->getFileIcon($dokumen->mime_type),
            'size_formatted' => $this->fileUploadService->formatFileSize($dokumen->ukuran_file),
            'preview_url' => route('file.preview', $dokumen->id),
            'download_url' => route('file.download', $dokumen->id)
        ];
    }

    /**
     * Delete dokumen (hanya untuk Draft atau Ditolak)
     */
    public function delete(int $dokumenId): array
    {
        $dokumen = Dokumen::with('pengajuan')->findOrFail($dokumenId);
        
        // Check permission - hanya owner yang bisa delete
        $user = auth()->user();
        if ($dokumen->pengajuan->user_id !== $user->id) {
            abort(403, 'Anda tidak memiliki akses untuk menghapus dokumen ini');
        }

        // Check status pengajuan
        if (!in_array($dokumen->pengajuan->status, ['Draft', 'Ditolak'])) {
            abort(403, 'Dokumen tidak dapat dihapus karena pengajuan sudah disubmit');
        }

        // Delete file from storage
        $this->fileUploadService->deleteFile($dokumen->path_file);
        
        // Delete from database
        $dokumen->delete();

        return [
            'success' => true,
            'message' => 'Dokumen berhasil dihapus'
        ];
    }

    /**
     * Batch upload dokumen
     */
    public function batchUpload(Request $request): array
    {
        $request->validate([
            'pengajuan_id' => 'required|integer|exists:pengajuan_pensiun,id',
            'files' => 'required|array',
            'files.*' => 'required|file|max:350|mimes:pdf,doc,docx,jpg,jpeg,png'
        ]);

        $pengajuan = $request->user()->pengajuanPensiun()->findOrFail($request->pengajuan_id);
        
        if (!in_array($pengajuan->status, ['Draft', 'Ditolak'])) {
            abort(403, 'Pengajuan tidak dapat diedit');
        }

        $results = [];
        $errors = [];

        foreach ($request->file('files') as $jenisDokumen => $file) {
            try {
                $fileInfo = $this->fileUploadService->uploadDokumenPengajuan(
                    $file,
                    $pengajuan->id,
                    $jenisDokumen
                );

                $dokumen = Dokumen::create([
                    'pengajuan_id' => $pengajuan->id,
                    'jenis_dokumen' => $jenisDokumen,
                    'nama_file' => $fileInfo['original_name'],
                    'path_file' => $fileInfo['path'],
                    'mime_type' => $fileInfo['mime_type'],
                    'ukuran_file' => $fileInfo['size'],
                    'wajib' => true
                ]);

                $results[] = [
                    'jenis_dokumen' => $jenisDokumen,
                    'dokumen' => $dokumen,
                    'file_info' => $fileInfo,
                    'success' => true
                ];

            } catch (\Exception $e) {
                $errors[] = [
                    'jenis_dokumen' => $jenisDokumen,
                    'error' => $e->getMessage(),
                    'success' => false
                ];
            }
        }

        return [
            'results' => $results,
            'errors' => $errors,
            'total_uploaded' => count($results),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Check if user can access dokumen
     */
    private function canAccessDokumen($user, Dokumen $dokumen): bool
    {
        // Kanwil dan Admin Pusat bisa akses semua
        if (in_array($user->role, ['Kanwil', 'Admin Pusat'])) {
            return true;
        }

        // Kabupaten hanya bisa akses pengajuan milik sendiri
        if ($user->role === 'Kabupaten') {
            return $dokumen->pengajuan->user_id === $user->id;
        }

        return false;
    }

    /**
     * Get storage statistics
     */
    public function storageStats(): array
    {
        // Hanya untuk Kanwil dan Admin Pusat
        $user = auth()->user();
        if (!in_array($user->role, ['Kanwil', 'Admin Pusat'])) {
            abort(403, 'Anda tidak memiliki akses ke statistik storage');
        }

        return $this->fileUploadService->getStorageStats();
    }
}
