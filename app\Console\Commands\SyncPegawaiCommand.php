<?php

namespace App\Console\Commands;

use App\Services\PegawaiService;
use Illuminate\Console\Command;

class SyncPegawaiCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pegawai:sync {--force : Force sync even if cache is fresh}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync data pegawai dari API external ke cache lokal';

    /**
     * Execute the console command.
     */
    public function handle(PegawaiService $pegawaiService)
    {
        $this->info('🔄 Memulai sync data pegawai...');

        // Check cache status
        if (!$this->option('force') && !$pegawaiService->isCacheExpired()) {
            $this->info('Cache masih fresh, skip sync. Gunakan --force untuk sync paksa.');
            return Command::SUCCESS;
        }

        $startTime = now();
        
        // Show current stats
        $stats = $pegawaiService->getCacheStats();
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Pegawai', $stats['total_pegawai']],
                ['Pegawai Aktif', $stats['pegawai_aktif']],
                ['TMT Hari Ini', $stats['pegawai_tmt_hari_ini']],
                ['Last Sync', $stats['last_sync'] ?: 'Never'],
            ]
        );

        $this->newLine();
        
        // Start sync with progress bar
        $bar = $this->output->createProgressBar();
        $bar->setFormat('verbose');
        $bar->start();

        $result = $pegawaiService->syncAllPegawai();
        
        $bar->finish();
        $this->newLine(2);

        // Show results
        if ($result['success']) {
            $duration = $startTime->diffInSeconds(now());
            
            $this->info("✅ Sync berhasil dalam {$duration} detik!");
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Data', $result['total']],
                    ['Berhasil Sync', $result['synced']],
                    ['Error', count($result['errors'])],
                ]
            );

            if (!empty($result['errors'])) {
                $this->newLine();
                $this->error('❌ Errors:');
                foreach ($result['errors'] as $error) {
                    $this->line("  • {$error}");
                }
            }

            // Show updated stats
            $this->newLine();
            $newStats = $pegawaiService->getCacheStats();
            $this->info('📊 Stats setelah sync:');
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Pegawai', $newStats['total_pegawai']],
                    ['Pegawai Aktif', $newStats['pegawai_aktif']],
                    ['TMT Hari Ini', $newStats['pegawai_tmt_hari_ini']],
                ]
            );

        } else {
            $this->error("❌ Sync gagal: {$result['error']}");
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
