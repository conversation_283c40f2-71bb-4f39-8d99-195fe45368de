import { Head } from '@inertiajs/react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Clock,
  Calendar
} from 'lucide-react'
import { formatDate } from '@/lib/utils'

interface StatistikData {
  total_pengajuan: number
  total_disetujui: number
  total_ditolak: number
  menunggu_review: number
  bulan_ini: number
}

interface CacheStats {
  total_pegawai: number
  last_sync: string | null
}

interface Props {
  statistik: StatistikData
  statistik_jenis: Record<string, any[]>
  statistik_bulanan: any[]
  cache_stats: CacheStats
}

export default function StatistikKanwil({ 
  statistik, 
  statistik_jenis, 
  statistik_bulanan, 
  cache_stats 
}: Props) {
  const tingkatApproval = statistik.total_pengajuan > 0 
    ? ((statistik.total_disetujui / statistik.total_pengajuan) * 100).toFixed(1)
    : '0.0'

  const getJenisPensiunLabel = (jenis: string) => {
    switch (jenis) {
      case 'BUP': return 'Batas Usia Pensiun'
      case 'Sakit': return 'Pensiun Sakit'
      case 'Janda/Duda': return 'Pensiun Janda/Duda'
      case 'APS': return 'Atas Permintaan Sendiri'
      default: return jenis
    }
  }

  const getBulanLabel = (bulan: number) => {
    const bulanNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',
      'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'
    ]
    return bulanNames[bulan - 1]
  }

  return (
    <>
      <Head title="Statistik Kanwil" />
      <AppLayout title="Statistik Kanwil">
        <div className="space-y-8 pb-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-slate-800 mb-2">Statistik Kanwil</h1>
            <p className="text-slate-600">Analisis dan monitoring pengajuan pensiun</p>
          </div>

          {/* Main Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Total Pengajuan</CardTitle>
                <div className="p-2 bg-blue-50 rounded-lg">
                  <FileText className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-800 mb-1">{statistik.total_pengajuan}</div>
                <p className="text-xs text-slate-500">
                  {statistik.bulan_ini} bulan ini
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Tingkat Approval</CardTitle>
                <div className="p-2 bg-emerald-50 rounded-lg">
                  <TrendingUp className="h-4 w-4 text-emerald-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-emerald-600 mb-1">{tingkatApproval}%</div>
                <p className="text-xs text-slate-500">
                  {statistik.total_disetujui} disetujui
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Menunggu Review</CardTitle>
                <div className="p-2 bg-orange-50 rounded-lg">
                  <Clock className="h-4 w-4 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600 mb-1">{statistik.menunggu_review}</div>
                <p className="text-xs text-slate-500">
                  Perlu ditindaklanjuti
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Cache Pegawai</CardTitle>
                <div className="p-2 bg-indigo-50 rounded-lg">
                  <Users className="h-4 w-4 text-indigo-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-800 mb-1">{cache_stats.total_pegawai}</div>
                <p className="text-xs text-slate-500">
                  {cache_stats.last_sync ? `Sync: ${formatDate(cache_stats.last_sync)}` : 'Belum sync'}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Status Breakdown */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Disetujui</CardTitle>
                <div className="p-2 bg-emerald-50 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-emerald-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-emerald-600 mb-1">{statistik.total_disetujui}</div>
                <p className="text-xs text-slate-500">
                  Pengajuan berhasil
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Ditolak</CardTitle>
                <div className="p-2 bg-red-50 rounded-lg">
                  <XCircle className="h-4 w-4 text-red-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600 mb-1">{statistik.total_ditolak}</div>
                <p className="text-xs text-slate-500">
                  Pengajuan ditolak
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Bulan Ini</CardTitle>
                <div className="p-2 bg-purple-50 rounded-lg">
                  <Calendar className="h-4 w-4 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-800 mb-1">{statistik.bulan_ini}</div>
                <p className="text-xs text-slate-500">
                  Pengajuan baru
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Statistik per Jenis Pensiun */}
          {Object.keys(statistik_jenis).length > 0 && (
            <Card className="border-0 shadow-sm bg-white mb-8">
              <CardHeader className="border-b border-slate-100 bg-slate-50/50">
                <CardTitle className="text-lg font-semibold text-slate-800">Statistik per Jenis Pensiun</CardTitle>
                <CardDescription className="text-slate-600">Distribusi pengajuan berdasarkan jenis pensiun</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {Object.entries(statistik_jenis).map(([jenis, data]) => {
                    const total = (data as any[]).reduce((sum, item) => sum + item.total, 0)
                    const disetujui = (data as any[]).find(item => item.status === 'Disetujui')?.total || 0
                    
                    return (
                      <div key={jenis} className="p-4 border border-slate-100 rounded-xl bg-slate-50/30 hover:bg-slate-50/50 transition-colors">
                        <div className="text-sm font-medium text-slate-600">{getJenisPensiunLabel(jenis)}</div>
                        <div className="text-2xl font-bold text-slate-800">{total}</div>
                        <div className="text-xs text-slate-500">
                          {disetujui} disetujui
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Statistik Bulanan */}
          {statistik_bulanan.length > 0 && (
            <Card className="border-0 shadow-sm bg-white">
              <CardHeader className="border-b border-slate-100 bg-slate-50/50">
                <CardTitle className="text-lg font-semibold text-slate-800">Tren 6 Bulan Terakhir</CardTitle>
                <CardDescription className="text-slate-600">Jumlah pengajuan per bulan</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                  {statistik_bulanan.map((item, index) => (
                    <div key={index} className="text-center p-4 border border-slate-100 rounded-xl bg-slate-50/30">
                      <div className="text-sm font-medium text-slate-600">
                        {getBulanLabel(item.bulan)} {item.tahun}
                      </div>
                      <div className="text-xl font-bold text-slate-800">{item.total}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </AppLayout>
    </>
  )
}
