import { Head } from '@inertiajs/react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  XCircle, 
  Download, 
  Eye, 
  ArrowLeft,
  FileText,
  User,
  Calendar,
  Building
} from 'lucide-react'
import { useState } from 'react'
import { formatDate, getStatusBadgeVariant, getStatusLabel, getJenisPensiunLabel } from '@/lib/utils'
import { PengajuanPensiun, User as UserType } from '@/types'

interface Props {
  pengajuan: PengajuanPensiun & {
    pegawai: {
      nama: string
      golongan: string
      unit_kerja: string
      induk_unit: string
      jabatan: string
      tmt_pensiun: string
    }
    user: UserType
    penyetuju?: UserType
    dokumen: Array<{
      id: number
      jenis_dokumen: string
      nama_file: string
      ukuran_file: number
      mime_type: string
      uploaded_at: string
    }>
    log_aktivitas: Array<{
      id: number
      aksi: string
      keterangan: string
      created_at: string
      user: UserType
    }>
  }
  user_role: 'Kanwil' | 'Kabupaten' | 'Admin Pusat'
  can_approve: boolean
}

export default function PengajuanDetail({ pengajuan, user_role, can_approve }: Props) {
  const [showApprovalForm, setShowApprovalForm] = useState(false)
  const [catatanPenolakan, setCatatanPenolakan] = useState('')
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve')

  const handleApproval = (action: 'approve' | 'reject') => {
    setActionType(action)
    if (action === 'reject') {
      setShowApprovalForm(true)
    } else {
      submitApproval(action)
    }
  }

  const submitApproval = (action: 'approve' | 'reject') => {
    const formData = new FormData()
    if (action === 'reject' && catatanPenolakan) {
      formData.append('catatan_penolakan', catatanPenolakan)
    }

    fetch(`/kanwil/pengajuan/${pengajuan.id}/${action}`, {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      }
    }).then(() => {
      window.location.reload()
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getBackUrl = () => {
    switch (user_role) {
      case 'Kanwil':
        return '/kanwil/dashboard'
      case 'Admin Pusat':
        return '/admin-pusat/dashboard'
      default:
        return '/dashboard'
    }
  }

  return (
    <AppLayout title={`Detail Pengajuan - ${pengajuan.pegawai.nama}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => window.location.href = getBackUrl()}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Kembali
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Detail Pengajuan Pensiun</h1>
              <p className="text-gray-600">NIP: {pengajuan.nip}</p>
            </div>
          </div>
          <Badge variant={getStatusBadgeVariant(pengajuan.status)} className="text-sm">
            {getStatusLabel(pengajuan.status)}
          </Badge>
        </div>

        {/* Data Pegawai */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Data Pegawai
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Nama Lengkap</label>
                  <p className="text-lg font-semibold">{pengajuan.pegawai.nama}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">NIP</label>
                  <p className="font-mono">{pengajuan.nip}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Golongan</label>
                  <p>{pengajuan.pegawai.golongan}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Jabatan</label>
                  <p>{pengajuan.pegawai.jabatan}</p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Unit Kerja</label>
                  <p>{pengajuan.pegawai.unit_kerja}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Induk Unit</label>
                  <p>{pengajuan.pegawai.induk_unit}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">TMT Pensiun</label>
                  <p className="font-semibold text-red-600">{formatDate(pengajuan.pegawai.tmt_pensiun)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Jenis Pensiun</label>
                  <p className="font-semibold">{getJenisPensiunLabel(pengajuan.jenis_pensiun)}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Pengajuan */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Informasi Pengajuan
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Tanggal Pengajuan</label>
                  <p>{formatDate(pengajuan.created_at)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Operator Pengaju</label>
                  <p>{pengajuan.user.name}</p>
                </div>
                {pengajuan.keterangan && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Keterangan</label>
                    <p className="whitespace-pre-wrap">{pengajuan.keterangan}</p>
                  </div>
                )}
              </div>
              <div className="space-y-4">
                {pengajuan.tanggal_disetujui && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      Tanggal {pengajuan.status === 'Disetujui' ? 'Disetujui' : 'Ditolak'}
                    </label>
                    <p>{formatDate(pengajuan.tanggal_disetujui)}</p>
                  </div>
                )}
                {pengajuan.penyetuju && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">
                      {pengajuan.status === 'Disetujui' ? 'Disetujui oleh' : 'Ditolak oleh'}
                    </label>
                    <p>{pengajuan.penyetuju.name}</p>
                  </div>
                )}
                {pengajuan.catatan_penolakan && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Catatan Penolakan</label>
                    <Alert className="mt-1">
                      <AlertDescription>{pengajuan.catatan_penolakan}</AlertDescription>
                    </Alert>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Dokumen */}
        <Card>
          <CardHeader>
            <CardTitle>Dokumen Pendukung</CardTitle>
            <CardDescription>Daftar dokumen yang dilampirkan</CardDescription>
          </CardHeader>
          <CardContent>
            {pengajuan.dokumen.length === 0 ? (
              <p className="text-gray-500 text-center py-8">Belum ada dokumen yang dilampirkan</p>
            ) : (
              <div className="space-y-3">
                {pengajuan.dokumen.map((dokumen) => (
                  <div key={dokumen.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{dokumen.jenis_dokumen}</div>
                      <div className="text-sm text-gray-500">
                        {dokumen.nama_file} • {formatFileSize(dokumen.ukuran_file)} •
                        Diupload {formatDate(dokumen.uploaded_at || dokumen.created_at)}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(`/file/preview/${dokumen.id}`, '_blank')}
                        className="gap-1"
                      >
                        <Eye className="h-4 w-4" />
                        Preview
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(`/file/download/${dokumen.id}`, '_blank')}
                        className="gap-1"
                      >
                        <Download className="h-4 w-4" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Log Aktivitas */}
        <Card>
          <CardHeader>
            <CardTitle>Riwayat Aktivitas</CardTitle>
            <CardDescription>Kronologi perubahan status pengajuan</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pengajuan.log_aktivitas.map((log, index) => (
                <div key={log.id} className="flex gap-4">
                  <div className="flex flex-col items-center">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    {index < pengajuan.log_aktivitas.length - 1 && (
                      <div className="w-px h-12 bg-gray-200 mt-2"></div>
                    )}
                  </div>
                  <div className="flex-1 pb-4">
                    <div className="flex items-center justify-between">
                      <p className="font-medium">{log.aksi}</p>
                      <span className="text-sm text-gray-500">{formatDate(log.created_at)}</span>
                    </div>
                    <p className="text-sm text-gray-600">{log.keterangan}</p>
                    <p className="text-xs text-gray-500">oleh {log.user.name}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Approval Actions */}
        {can_approve && pengajuan.status === 'Diajukan' && (
          <Card>
            <CardHeader>
              <CardTitle>Tindakan Persetujuan</CardTitle>
              <CardDescription>Setujui atau tolak pengajuan pensiun ini</CardDescription>
            </CardHeader>
            <CardContent>
              {!showApprovalForm ? (
                <div className="flex gap-4">
                  <Button
                    onClick={() => handleApproval('approve')}
                    className="gap-2 bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4" />
                    Setujui Pengajuan
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleApproval('reject')}
                    className="gap-2"
                  >
                    <XCircle className="h-4 w-4" />
                    Tolak Pengajuan
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <label htmlFor="catatan" className="block text-sm font-medium text-gray-700 mb-2">
                      Catatan Penolakan (opsional)
                    </label>
                    <Textarea
                      id="catatan"
                      value={catatanPenolakan}
                      onChange={(e) => setCatatanPenolakan(e.target.value)}
                      placeholder="Berikan alasan penolakan..."
                      rows={3}
                    />
                  </div>
                  <div className="flex gap-4">
                    <Button
                      onClick={() => submitApproval('reject')}
                      variant="destructive"
                      className="gap-2"
                    >
                      <XCircle className="h-4 w-4" />
                      Konfirmasi Tolak
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setShowApprovalForm(false)}
                    >
                      Batal
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  )
}
