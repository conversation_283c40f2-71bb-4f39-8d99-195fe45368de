import { User } from '@/types'
import { cn } from '@/lib/utils'
import { 
  LayoutDashboard, 
  Users, 
  FileText, 
  CheckCircle, 
  BarChart3,
  Calendar,
  Settings 
} from 'lucide-react'

interface Props {
  user: User
}

interface MenuItem {
  label: string
  href: string
  icon: any
  roles: string[]
}

const menuItems: MenuItem[] = [
  {
    label: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    roles: ['Kanwil', 'Kabupaten', 'Admin Pusat']
  },
  {
    label: 'Data Pegawai',
    href: '/kabupaten/pegawai',
    icon: Users,
    roles: ['Kabupaten']
  },
  {
    label: 'Pengajuan Pensiun',
    href: '/kabupaten/pengajuan',
    icon: FileText,
    roles: ['Kabupaten']
  },
  {
    label: 'Review Pengajuan',
    href: '/kanwil/pengajuan',
    icon: CheckCircle,
    roles: ['Kanwil']
  },
  {
    label: 'Statistik',
    href: '/admin-pusat/statistik',
    icon: BarChart3,
    roles: ['Admin Pusat', 'Kanwil']
  },
  {
    label: 'La<PERSON><PERSON>',
    href: '/admin-pusat/laporan',
    icon: Calendar,
    roles: ['Admin Pusat', 'Kanwil']
  }
]

export default function Sidebar({ user }: Props) {
  const currentPath = window.location.pathname

  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(user.role)
  )

  return (
    <aside className="fixed left-0 top-16 w-64 h-[calc(100vh-4rem)] bg-white shadow-sm border-r border-slate-200 overflow-y-auto z-30">
      <div className="p-6">
        <nav className="space-y-1">
          {filteredMenuItems.map((item) => {
            const Icon = item.icon
            const isActive = currentPath === item.href || currentPath.startsWith(item.href + '/')

            return (
              <a
                key={item.href}
                href={item.href}
                className={cn(
                  "flex items-center space-x-3 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200",
                  isActive
                    ? "bg-blue-50 text-blue-700 shadow-sm border border-blue-100"
                    : "text-slate-700 hover:bg-slate-50 hover:text-slate-900"
                )}
              >
                <Icon className="h-5 w-5" />
                <span>{item.label}</span>
              </a>
            )
          })}
        </nav>
      </div>

      {/* Footer */}
      <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-slate-100 bg-slate-50/50">
        <div className="flex items-center space-x-3 text-xs text-slate-500">
          <Settings className="h-4 w-4" />
          <span>v1.0.0</span>
        </div>
      </div>
    </aside>
  )
}
