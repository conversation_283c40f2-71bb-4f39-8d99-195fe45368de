import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import FilePreviewModal from './FilePreviewModal'
import { 
  Upload, 
  Eye, 
  X, 
  FileText, 
  Image as ImageIcon,
  AlertCircle,
  Check
} from 'lucide-react'

interface FileUploadFieldProps {
  label: string
  jenis_dokumen: string
  file: File | null
  onFileChange: (file: File | null) => void
  error?: string
  required?: boolean
  maxSizeKB?: number
  className?: string
}

export default function FileUploadField({
  label,
  jenis_dokumen,
  file,
  onFileChange,
  error,
  required = false,
  maxSizeKB = 350,
  className = ''
}: FileUploadFieldProps) {
  const [showPreview, setShowPreview] = useState(false)

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <ImageIcon className="h-5 w-5 text-blue-500" />
    }
    return <FileText className="h-5 w-5 text-red-500" />
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      onFileChange(selectedFile)
    }
  }

  const handleRemoveFile = () => {
    onFileChange(null)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={`file-${jenis_dokumen}`} className="flex items-center gap-2">
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>

      {/* File Upload State */}
      {!file ? (
        <div className="space-y-2">
          <input
            id={`file-${jenis_dokumen}`}
            type="file"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            onChange={handleFileSelect}
            className="hidden"
          />
          
          <Button
            type="button"
            variant="outline"
            onClick={() => document.getElementById(`file-${jenis_dokumen}`)?.click()}
            className="w-full h-20 border-2 border-dashed hover:border-gray-400 gap-3"
          >
            <Upload className="h-6 w-6 text-gray-400" />
            <div className="text-center">
              <p className="font-medium">Upload {label}</p>
              <p className="text-xs text-gray-500">PDF, DOC, DOCX, JPG, PNG (Maks. {maxSizeKB} KB)</p>
            </div>
          </Button>
        </div>
      ) : (
        /* File Uploaded State */
        <div className="border rounded-lg p-4 bg-green-50 border-green-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                {getFileIcon(file.type)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-green-800 truncate">{file.name}</p>
                <p className="text-sm text-green-600">
                  {formatFileSize(file.size)} • Siap untuk diupload
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                type="button"
                size="sm"
                variant="outline"
                onClick={() => setShowPreview(true)}
                className="gap-2"
              >
                <Eye className="h-4 w-4" />
                Preview
              </Button>
              <Button
                type="button"
                size="sm"
                variant="outline"
                onClick={handleRemoveFile}
                className="gap-2 text-red-600 hover:text-red-700"
              >
                <X className="h-4 w-4" />
                Hapus
              </Button>
            </div>
          </div>
          
          {/* Success checkmark */}
          <div className="flex items-center gap-2 mt-2">
            <Check className="h-4 w-4 text-green-600" />
            <span className="text-sm text-green-700">File berhasil dipilih</span>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* File Requirements */}
      <div className="text-xs text-gray-500 space-y-1">
        <p>• Format yang didukung: PDF, DOC, DOCX, JPG, PNG</p>
        <p>• Ukuran maksimal: {maxSizeKB} KB</p>
        <p>• Pastikan file jelas dan dapat dibaca</p>
      </div>

      {/* File Preview Modal */}
      <FilePreviewModal
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        file={file}
        jenis_dokumen={jenis_dokumen}
        onFileChange={onFileChange}
        maxSizeKB={maxSizeKB}
      />
    </div>
  )
}
