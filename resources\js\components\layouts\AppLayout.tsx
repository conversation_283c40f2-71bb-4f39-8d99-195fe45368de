import { ReactNode } from 'react'
import { Head, usePage } from '@inertiajs/react'
import { User, PageProps } from '@/types'
import Navbar from './Navbar'
import Sidebar from './Sidebar'

interface Props {
  children: ReactNode
  title?: string
}

export default function AppLayout({ children, title }: Props) {
  const { auth } = usePage().props as unknown as PageProps

  return (
    <>
      <Head title={title} />
      
      <div className="min-h-screen bg-slate-50">
        <Navbar user={auth.user} />

        <div className="flex pt-16">
          <Sidebar user={auth.user} />

          <main className="flex-1 ml-64 p-4 sm:p-6 lg:p-8 min-h-[calc(100vh-4rem)]">
            <div className="max-w-7xl mx-auto w-full">
              {children}
            </div>
          </main>
        </div>
      </div>
    </>
  )
}
