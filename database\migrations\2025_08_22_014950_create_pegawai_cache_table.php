<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pegawai_cache', function (Blueprint $table) {
            $table->id();
            $table->string('nip')->unique(); // NIP sebagai unique key
            $table->string('nama');
            $table->string('golongan');
            $table->dateTime('tmt_pensiun'); // TMT Pensiun untuk filtering
            $table->string('unit_kerja');
            $table->string('induk_unit');
            $table->string('jabatan');
            // kantor_id removed - use user_id for filtering
            $table->string('jenis_pegawai');
            $table->boolean('aktif')->default(true);
            $table->dateTime('last_sync')->nullable(); // Kapan terakhir sync dari API
            $table->timestamps();
            
            $table->index(['tmt_pensiun', 'aktif']); // Index untuk query filtering
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pegawai_cache');
    }
};
