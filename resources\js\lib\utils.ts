import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date): string {
  const d = new Date(date)
  return d.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  })
}

export function formatDateTime(date: string | Date): string {
  const d = new Date(date)
  return d.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: 'long',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export function formatFileSize(bytes: number): string {
  if (bytes >= 1048576) {
    return `${(bytes / 1048576).toFixed(2)} MB`
  } else if (bytes >= 1024) {
    return `${(bytes / 1024).toFixed(2)} KB`
  } else {
    return `${bytes} bytes`
  }
}

export function getStatusBadgeVariant(status: string): 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' | 'info' | 'danger' {
  switch (status) {
    case 'Draft':
      return 'outline'
    case 'Diajukan':
      return 'warning'
    case 'Disetujui':
      return 'success'
    case 'Ditolak':
      return 'danger'
    default:
      return 'outline'
  }
}

export function getStatusLabel(status: string): string {
  switch (status) {
    case 'Draft':
      return 'Draft'
    case 'Diajukan':
      return 'Menunggu Review'
    case 'Disetujui':
      return 'Disetujui'
    case 'Ditolak':
      return 'Ditolak'
    default:
      return status
  }
}

export function getJenisPensiunLabel(jenis: string): string {
  switch (jenis) {
    case 'BUP':
      return 'Batas Usia Pensiun (BUP)'
    case 'Sakit':
      return 'Pensiun Sakit'
    case 'Janda/Duda':
      return 'Pensiun Janda/Duda'
    case 'APS':
      return 'Alih Tugas (APS)'
    default:
      return jenis
  }
}

export function getDokumenWajib(jenisPensiun: string): string[] {
  const dokumenWajib = [
    'Pengantar',
    'DPCP',
    'SK CPNS',
    'SKKP Terakhir',
    'Super HD',
    'Super Pidana',
    'Pas Foto',
    'Buku Nikah',
    'Kartu Keluarga',
    'SKP Terakhir'
  ]

  const dokumenTambahan = {
    'Sakit': ['Surat Keterangan Sakit'],
    'Janda/Duda': ['Akta Kematian', 'Suket Janda/Duda', 'Pas Foto Pasangan'],
    'APS': ['Surat Usul Pemberhentian', 'Surat Permohonan PPS']
  }

  return [...dokumenWajib, ...(dokumenTambahan[jenisPensiun as keyof typeof dokumenTambahan] || [])]
}

export function isValidFileType(file: File): boolean {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png'
  ]
  return allowedTypes.includes(file.type)
}

export function isValidFileSize(file: File): boolean {
  return file.size <= 358400 // 350KB
}

export function validateFile(file: File): string[] {
  const errors: string[] = []
  
  if (!isValidFileType(file)) {
    errors.push('Tipe file tidak diizinkan. Gunakan PDF, DOC, DOCX, JPG, atau PNG')
  }
  
  if (!isValidFileSize(file)) {
    errors.push('Ukuran file maksimal 350KB')
  }
  
  return errors
}

export function truncateText(text: string, maxLength: number = 50): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

export function capitalize(text: string): string {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
}

export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}
