<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PegawaiService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PegawaiController extends Controller
{
    public function __construct(
        private PegawaiService $pegawaiService
    ) {}

    /**
     * Get all pegawai from cache
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $kantorId = $request->user()->kantor_id;
            $pegawaiList = $this->pegawaiService->getPegawaiTmtHariIni($kantorId);

            return response()->json([
                'success' => true,
                'data' => $pegawaiList,
                'total' => $pegawaiList->count(),
                'cache_stats' => $this->pegawaiService->getCacheStats()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data pegawai',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pegawai by NIP
     */
    public function show(string $nip): JsonResponse
    {
        try {
            $pegawai = $this->pegawaiService->getPegawaiByNip($nip);

            if (!$pegawai) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pegawai tidak ditemukan'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $pegawai
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data pegawai',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync pegawai data from external API
     */
    public function sync(): JsonResponse
    {
        try {
            $result = $this->pegawaiService->syncAllPegawai();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Sync berhasil' : 'Sync gagal',
                'data' => $result
            ], $result['success'] ? 200 : 500);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal melakukan sync',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pegawai yang TMT pensiun hari ini untuk Kabupaten dashboard
     */
    public function tmtHariIni(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $kantorId = $user->isKabupaten() ? $user->kantor_id : null;
            
            $pegawaiList = $this->pegawaiService->getPegawaiTmtHariIni($kantorId);

            // Add pengajuan status for each pegawai
            $pegawaiWithStatus = $pegawaiList->map(function ($pegawai) {
                return [
                    ...$pegawai->toArray(),
                    'has_pengajuan_aktif' => $pegawai->hasPengajuanAktif(),
                    'pengajuan_count' => $pegawai->pengajuanPensiun()->count()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $pegawaiWithStatus,
                'total' => $pegawaiWithStatus->count(),
                'message' => "Ditemukan {$pegawaiWithStatus->count()} pegawai yang TMT pensiun"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data pegawai TMT',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get cache statistics
     */
    public function stats(): JsonResponse
    {
        try {
            $stats = $this->pegawaiService->getCacheStats();

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil statistik',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
