<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dokumen', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pengajuan_id')->constrained('pengajuan_pensiun')->onDelete('cascade');
            $table->enum('jenis_dokumen', [
                'Pengantar', 'DPCP', 'SK CPNS', 'SKKP Terakhir', 'Super HD', 
                'Super Pidana', 'Pas Foto', 'Buku Nikah', 'Kartu Keluarga', 'SKP Terakhir',
                // Dokumen tambahan per jenis pensiun
                'Surat Keterangan Sakit', 'Akta Kematian', 'Suket Janda/Duda', 
                'Pas Foto Pasangan', 'Surat Usul Pemberhentian', 'Surat Permohonan PPS'
            ]);
            $table->string('nama_file');
            $table->string('path_file');
            $table->string('mime_type');
            $table->integer('ukuran_file'); // dalam bytes
            $table->boolean('wajib')->default(true);
            $table->timestamps();
            
            $table->index(['pengajuan_id', 'jenis_dokumen']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dokumen');
    }
};
