<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Dokumen extends Model
{
    protected $table = 'dokumen';
    
    protected $fillable = [
        'pengajuan_id',
        'jenis_dokumen',
        'nama_file',
        'path_file',
        'mime_type',
        'ukuran_file',
        'wajib'
    ];

    protected $casts = [
        'wajib' => 'boolean',
    ];

    // Relationship dengan PengajuanPensiun
    public function pengajuan()
    {
        return $this->belongsTo(PengajuanPensiun::class, 'pengajuan_id');
    }

    // Get URL untuk download/preview file
    public function getFileUrlAttribute()
    {
        return Storage::url($this->path_file);
    }

    // Check apakah file ada di storage
    public function fileExists()
    {
        return Storage::exists($this->path_file);
    }

    // Get ukuran file dalam format readable
    public function getFormattedFileSizeAttribute()
    {
        $bytes = $this->ukuran_file;
        
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    // Scope untuk dokumen wajib
    public function scopeWajib($query)
    {
        return $query->where('wajib', true);
    }

    // Scope untuk jenis dokumen tertentu
    public function scopeJenisDokumen($query, $jenis)
    {
        return $query->where('jenis_dokumen', $jenis);
    }

    // Validasi ukuran file (max 350KB)
    public function isValidFileSize()
    {
        return $this->ukuran_file <= 358400; // 350KB in bytes
    }

    // Delete file dari storage ketika model dihapus
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($dokumen) {
            if ($dokumen->fileExists()) {
                Storage::delete($dokumen->path_file);
            }
        });
    }
}
