# 📘 Panduan Aplikasi Pengajuan Pensiun ASN

## 🎯 Tujuan Sistem
Aplikasi ini digunakan untuk memfasilitasi pengajuan pensiun ASN oleh instansi terkait dengan memverifikasi kelengkapan dokumen dan alur persetujuan oleh pihak berwenang.

---

## Data pegawai

## Base URL
```
http://localhost:3000/api/public/employees
```

## Endpoints

### 1. Get All Pegawai
**GET** `/api/public/employees`

Mengambil semua data pegawai.

**Response Success (200):**
```json
{
  "pegawai": [
    {
      "nip": "198501012010011001",
      "nama": "<PERSON> R<PERSON>",
      "golongan": "III/a",
      "tmt_pensiun": "2025-01-01T00:00:00.000Z",
      "unit_kerja": "Bidang Mutasi",
      "induk_unit": "Kantor Wilayah",
      "jabatan": "Kepala Seksi",
      "kantor_id": "550e8400-e29b-41d4-a716-446655440000",
      "jenis_pegawai": "pegawai",
      "aktif": true,
      "dibuat_pada": "2024-01-01T00:00:00.000Z",
      "diubah_pada": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

**Response Error (401):**
```json
{
  "message": "Unauthorized"
}
```

---

### 2. Get Pegawai by ID
**GET** `/api/public/employees/:id`

Mengambil data pegawai berdasarkan NIP.


**Parameters:**
- `id` (string, required): NIP pegawai

**Response Success (200):**
```json
{
  "pegawai": {
    "nip": "198501012010011001",
    "nama": "Ahmad Rival",
    "golongan": "III/a",
    "tmt_pensiun": "2025-01-01T00:00:00.000Z",
    "unit_kerja": "Bidang Mutasi",
    "induk_unit": "Kantor Wilayah",
    "jabatan": "Kepala Seksi",
    "kantor_id": "550e8400-e29b-41d4-a716-446655440000",
    "jenis_pegawai": "pegawai",
    "aktif": true,
    "dibuat_pada": "2024-01-01T00:00:00.000Z",
    "diubah_pada": "2024-01-01T00:00:00.000Z"
  }
}
```

**Response Error (404):**
```json
{
  "message": "Pegawai not found"
}
```


pegawai yang ditampilkan dalam datatabel sesuai dengan dibawah ini yaitu yang tmt pensiunnya <= har ini

## 🖥️ Dashboard Kabupaten (Operator)

### Tampilan:
- Menampilkan pegawai dengan `TMT Pensiun <= hari ini`
- Checkbox di setiap baris pegawai (data table)

### Tombol "Ajukan Pensiun":
- **Default:** Tidak aktif
- **Aktif jika:** Ada pegawai yang dicentang
- **Aksi:**
  - Jika 1 pegawai dipilih → lanjut ke form pengajuan
  - Jika lebih dari 1 → tampilkan notifikasi: "Silakan pilih satu pegawai untuk diajukan"
  - Jika pegawai sudah diajukan → notifikasi: "Pegawai ini sudah memiliki pengajuan aktif"

---

## 👥 Role Pengguna

| Role           | Deskripsi                                                                 |
|----------------|--------------------------------------------------------------------------|
| **Kanwil**     | Superadmin: akses semua menu sistem |
| **Kabupaten**  | Operator: mengajukan pensiun dan mengelola dokumen pensiun                     |
| **Admin Pusat**| Viewer: hanya dapat melihat seluruh pengajuan dan status.                |

---

## 🗃️ Jenis Pensiun dan Dokumen Wajib

### ✅ 10 Dokumen Wajib (semua jenis pensiun):
1. Pengantar
2. DPCP
3. SK CPNS
4. SKKP Terakhir
5. Super HD
6. Super Pidana
7. Pas Foto
8. Buku Nikah
9. Kartu Keluarga (KK)
10. SKP Terakhir

### ➕ Tambahan Berdasarkan Jenis Pensiun:
| Jenis Pensiun | Dokumen Tambahan                                                         |
|---------------|--------------------------------------------------------------------------|
| **BUP**       | -                                                                        |
| **Sakit**     | Surat Keterangan Sakit dari Dokter                                       |
| **Janda/Duda**| Akta/Suket Kematian, Suket Janda/Duda, Pas Foto Pasangan                 |
| **APS**       | Surat Usul Pemberhentian dari PPK, Surat Permohonan menjadi PPS          |

---


## 📤 Form Pengajuan

1. Pilih **jenis pensiun**
2. Tampilkan form upload sesuai jenis
3. Validasi ukuran file: **maks 350KB**
4. Tampilkan **preview** file (dalam modal)
5. File bisa diganti sebelum submit
6. Klik **Submit** → status berubah menjadi `Diajukan`

---

## ✅ Persetujuan oleh Kanwil

- Lihat semua pengajuan
- Aksi:
  - **Setujui** → status jadi `Disetujui`
  - **Tolak** → wajib isi alasan penolakan
    - Operator bisa mengedit file dan **ajukan ulang**

---

## 🔁 Log Aktivitas

Semua aksi pengguna dicatat:
- Siapa
- Kapan
- Aksi
- Detail

---

## 📊 Tampilan Berdasarkan Role

### Kabupaten:
- Lihat & ajukan pegawai pensiun
- Edit file jika ditolak

### Kanwil:
- Lihat semua pengajuan
- Setujui/Tolak pengajuan
- Lihat alasan dan riwayat pengajuan

### Admin Pusat:
- View-only seluruh data

---

## 📌 Catatan Teknis

- Form upload hanya muncul setelah memilih jenis pensiun
- Preview file dalam modal, bisa diganti sebelum submit
- Alasan penolakan bersifat wajib
- Pengajuan ulang hanya aktif setelah revisi

---

## 🔒 Validasi File

- Maksimum ukuran file: **350KB**
- Format disarankan: **PDF**
- Semua file wajib ada sebelum submit

---

