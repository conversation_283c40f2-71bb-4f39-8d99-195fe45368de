import { Head } from '@inertiajs/react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Building2, 
  Shield, 
  Clock, 
  FileText,
  CheckCircle,
  Users,
  ArrowRight,
  Star
} from 'lucide-react'

export default function Welcome() {
  const features = [
    {
      icon: <FileText className="h-6 w-6 text-blue-600" />,
      title: "Pengajuan Digital",
      description: "Ajukan pensiun secara online dengan mudah dan cepat"
    },
    {
      icon: <Shield className="h-6 w-6 text-green-600" />,
      title: "Keamanan Terjamin", 
      description: "Sistem keamanan berlapis untuk melindungi data Anda"
    },
    {
      icon: <Clock className="h-6 w-6 text-orange-600" />,
      title: "Proses Cepat",
      description: "Tracking real-time status pengajuan pensiun Anda"
    },
    {
      icon: <CheckCircle className="h-6 w-6 text-purple-600" />,
      title: "Approval Workflow",
      description: "Sistem persetujuan bertingkat yang terstruktur"
    }
  ]

  return (
    <>
      <Head title="Welcome" />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
                  <Building2 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">SIPENSIUN</h1>
                  <p className="text-sm text-gray-600">Kemenag RI</p>
                </div>
              </div>
              <Button
                onClick={() => window.location.href = '/login'}
                className="bg-green-600 hover:bg-green-700 gap-2"
              >
                Masuk Sistem
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Hero Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center max-w-4xl mx-auto">
            <Badge variant="secondary" className="mb-6 px-4 py-2 text-sm">
              <Star className="h-4 w-4 mr-2" />
              Sistem Terpadu Pengajuan Pensiun ASN
            </Badge>
            
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Sistem Pengajuan <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600">
                Pensiun ASN
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-4 max-w-2xl mx-auto">
              Platform digital untuk memudahkan proses pengajuan pensiun 
              Aparatur Sipil Negara di lingkungan Kementerian Agama
            </p>
            
            <div className="flex items-center justify-center gap-2 mb-12">
              <Building2 className="h-5 w-5 text-green-600" />
              <span className="text-lg font-semibold text-green-700">
                Kantor Wilayah Kementerian Agama NTB
              </span>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                onClick={() => window.location.href = '/login'}
                className="bg-green-600 hover:bg-green-700 px-8 py-4 text-lg gap-2"
              >
                Mulai Pengajuan
                <ArrowRight className="h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="px-8 py-4 text-lg"
                onClick={() => {
                  document.getElementById('features')?.scrollIntoView({ 
                    behavior: 'smooth' 
                  })
                }}
              >
                Pelajari Lebih Lanjut
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="bg-white/50 backdrop-blur-sm py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">100%</div>
                <div className="text-gray-600">Digital</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">24/7</div>
                <div className="text-gray-600">Akses Sistem</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-purple-600 mb-2">Real-time</div>
                <div className="text-gray-600">Status Tracking</div>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div id="features" className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Fitur Unggulan
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Sistem yang dirancang khusus untuk memudahkan proses pengajuan pensiun ASN
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <Card key={index} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/70 backdrop-blur-sm">
                  <CardContent className="p-6 text-center">
                    <div className="mb-4 flex justify-center">
                      <div className="p-3 rounded-full bg-gray-50 group-hover:scale-110 transition-transform duration-300">
                        {feature.icon}
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-green-600 to-blue-600 py-16">
          <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Siap Mengajukan Pensiun?
            </h2>
            <p className="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
              Bergabunglah dengan ribuan ASN yang telah merasakan kemudahan sistem digital kami
            </p>
            <Button
              size="lg"
              onClick={() => window.location.href = '/login'}
              className="bg-white text-green-600 hover:bg-gray-50 px-8 py-4 text-lg font-semibold gap-2"
            >
              <Users className="h-5 w-5" />
              Masuk ke Sistem
            </Button>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-900 py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                  <Building2 className="h-5 w-5 text-white" />
                </div>
                <span className="text-white font-semibold">SIPENSIUN</span>
              </div>
              <p className="text-gray-400 mb-2">
                Sistem Pengajuan Pensiun ASN
              </p>
              <p className="text-gray-400 text-sm">
                Kementerian Agama Republik Indonesia
              </p>
              <p className="text-gray-500 text-sm mt-4">
                © 2025 Kanwil Kemenag NTB. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}