export interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    role: 'Kanwil' | 'Kabupaten' | 'Admin P<PERSON>t';

}

export interface PegawaiCache {
    id: number;
    nip: string;
    nama: string;
    golongan: string;
    tmt_pensiun: string;
    unit_kerja: string;
    induk_unit: string;
    jabatan: string;

    jenis_pegawai: string;
    aktif: boolean;
    last_sync: string | null;
    created_at: string;
    updated_at: string;
    has_pengajuan_aktif?: boolean;
    pengajuan_status?: string;
    pengajuan_aktif?: PengajuanPensiun | null;
    total_pengajuan?: number;
}

export interface PengajuanPensiun {
    id: number;
    nip: string;
    user_id: number;
    jenis_pensiun: 'BUP' | 'Sakit' | 'Janda/Duda' | 'APS';
    status: 'Draft' | 'Diajukan' | 'Disetujui' | 'Ditolak';
    catatan_penolakan: string | null;
    keterangan?: string | null;
    disetujui_oleh: number | null;
    tanggal_disetujui: string | null;
    created_at: string;
    updated_at: string;
    pegawai?: PegawaiCache;
    user?: User;
    penyetuju?: User;
    dokumen?: Dokumen[];
}

export interface Dokumen {
    id: number;
    pengajuan_id: number;
    jenis_dokumen: string;
    nama_file: string;
    path_file: string;
    mime_type: string;
    ukuran_file: number;
    wajib: boolean;
    uploaded_at?: string;
    created_at: string;
    updated_at: string;
    pengajuan?: PengajuanPensiun;
}

export interface LogAktivitas {
    id: number;
    user_id: number;
    aksi: string;
    entitas: string;
    entitas_id: number | null;
    detail: string | null;
    data_lama: any | null;
    data_baru: any | null;
    ip_address: string | null;
    user_agent: string | null;
    created_at: string;
    updated_at: string;
    user?: User;
}

export interface PageProps<T extends Record<string, unknown> = Record<string, unknown>> {
    auth: {
        user: User;
    };
    errors: Record<string, string>;
    flash: {
        message?: string;
        success?: string;
        error?: string;
    };
    ziggy?: {
        routes: Record<string, any>;
        url: string;
        port: number | null;
    };
}

// Dashboard specific props
export interface KabupatenDashboardProps extends PageProps {
    pegawai_tmt: PegawaiCache[];
    my_pengajuan: PengajuanPensiun[];
    statistik: {
        pegawai_tmt_hari_ini: number;
        pegawai_sudah_diajukan: number;
        my_pengajuan_draft: number;
        my_pengajuan_diajukan: number;
        my_pengajuan_disetujui: number;
        my_pengajuan_ditolak: number;
    };
    cache_stats: {
        total_pegawai: number;
        pegawai_aktif: number;
        pegawai_tmt_hari_ini: number;
        last_sync: string | null;
        cache_expired: boolean;
    };
}

export interface KanwilDashboardProps extends PageProps {
    pengajuan_list: {
        data: PengajuanPensiun[];
        current_page: number;
        last_page: number;
        total: number;
    };
    statistik: {
        total_pengajuan: number;
        menunggu_review: number;
        disetujui_bulan_ini: number;
        ditolak_bulan_ini: number;
    };
    statistik_jenis: Record<string, any>;
    cache_stats: any;
    filters: {
        status?: string;
        jenis_pensiun?: string;
    };
    status_options: string[];
    jenis_pensiun_options: string[];
}

export interface AdminPusatDashboardProps extends PageProps {
    pengajuan_list: {
        data: PengajuanPensiun[];
        current_page: number;
        last_page: number;
        total: number;
    };
    statistik: {
        total_pengajuan: number;
        total_disetujui: number;
        total_ditolak: number;
        menunggu_review: number;
        bulan_ini: number;
        tahun_ini: number;
        rata_waktu_approval: number;
    };
    statistik_bulanan: Record<string, any>;
    statistik_jenis: Record<string, any>;
    cache_stats: any;
    filters: {
        status?: string;
        jenis_pensiun?: string;
        periode?: string;
    };
    status_options: string[];
    jenis_pensiun_options: string[];
    periode_options: Record<string, string>;
}

export interface PengajuanCreateProps extends PageProps {
    pegawai: PegawaiCache;
    jenis_pensiun_options: string[];
}

export interface PengajuanEditProps extends PageProps {
    pengajuan: PengajuanPensiun;
    dokumen_wajib: string[];
    dokumen_kurang: string[];
    jenis_pensiun_options: string[];
}

export interface PengajuanDetailProps extends PageProps {
    pengajuan: PengajuanPensiun;
    dokumen_wajib: string[];
    can_approve: boolean;
    log_aktivitas: LogAktivitas[];
}
