import { Head } from '@inertiajs/react'
import { useState } from 'react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Search, Eye, FileText, Plus, Edit, Trash2 } from 'lucide-react'
import { formatDate, getStatusBadgeVariant, getStatusLabel, getJenisPensiunLabel } from '@/lib/utils'
import { PengajuanPensiun } from '@/types'

interface Props {
  pengajuan_list: {
    data: PengajuanPensiun[]
    current_page: number
    last_page: number
    total: number
  }
  filters: {
    status?: string
    jenis_pensiun?: string
    search?: string
  }
  status_options: string[]
  jenis_pensiun_options: string[]
}

export default function PengajuanIndex({ 
  pengajuan_list, 
  filters, 
  status_options, 
  jenis_pensiun_options 
}: Props) {
  const [search, setSearch] = useState(filters.search || '')
  const [statusFilter, setStatusFilter] = useState(filters.status || 'all')
  const [jenisFilter, setJenisFilter] = useState(filters.jenis_pensiun || 'all')

  const handleFilterChange = () => {
    const params = new URLSearchParams()
    if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter)
    if (jenisFilter && jenisFilter !== 'all') params.append('jenis_pensiun', jenisFilter)
    if (search) params.append('search', search)
    
    window.location.href = `/kanwil/pengajuan-kelola?${params.toString()}`
  }

  const handleResetFilter = () => {
    setStatusFilter('all')
    setJenisFilter('all')
    setSearch('')
    window.location.href = '/kanwil/pengajuan-kelola'
  }

  const handleDelete = (id: number) => {
    if (confirm('Apakah Anda yakin ingin menghapus pengajuan ini?')) {
      fetch(`/kanwil/pengajuan-kelola/${id}`, {
        method: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
      }).then(() => {
        window.location.reload()
      })
    }
  }

  return (
    <>
      <Head title="Kelola Pengajuan - Kanwil" />
      <AppLayout title="Kelola Pengajuan">
        <div className="space-y-8 pb-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-slate-800 mb-2">Kelola Pengajuan</h1>
                <p className="text-slate-600">Kelola semua pengajuan pensiun sebagai superadmin</p>
              </div>
              <Button
                onClick={() => window.location.href = '/kanwil/pengajuan/create'}
                className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Buat Pengajuan
              </Button>
            </div>
          </div>

          {/* Pengajuan Table */}
          <Card className="border-0 shadow-sm bg-white">
            <CardHeader className="border-b border-slate-100 bg-slate-50/50">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-slate-800">Daftar Pengajuan Pensiun</CardTitle>
                  <CardDescription className="text-slate-600">
                    Kelola dan monitor semua pengajuan pensiun
                  </CardDescription>
                </div>
              </div>
              
              {/* Filters */}
              <div className="flex flex-wrap items-center gap-4 pt-4">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                  <Input
                    placeholder="Cari pengajuan..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px] border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                    <SelectValue placeholder="Semua Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Status</SelectItem>
                    {status_options.map((status) => (
                      <SelectItem key={status} value={status}>
                        {getStatusLabel(status)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={jenisFilter} onValueChange={setJenisFilter}>
                  <SelectTrigger className="w-[180px] border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                    <SelectValue placeholder="Semua Jenis" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Jenis</SelectItem>
                    {jenis_pensiun_options.map((jenis) => (
                      <SelectItem key={jenis} value={jenis}>
                        {getJenisPensiunLabel(jenis)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button 
                  onClick={handleFilterChange}
                  className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
                >
                  Filter
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleResetFilter}
                  className="border-slate-200 text-slate-600 hover:bg-slate-50"
                >
                  Reset
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="border-b border-slate-100 bg-slate-50/30">
                      <TableHead className="font-semibold text-slate-700">NIP</TableHead>
                      <TableHead className="font-semibold text-slate-700">Nama Pegawai</TableHead>
                      <TableHead className="font-semibold text-slate-700">Jenis Pensiun</TableHead>
                      <TableHead className="font-semibold text-slate-700">Tanggal Pengajuan</TableHead>
                      <TableHead className="font-semibold text-slate-700">Status</TableHead>
                      <TableHead className="font-semibold text-slate-700">Operator</TableHead>
                      <TableHead className="text-right font-semibold text-slate-700">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pengajuan_list.data.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-12 text-slate-500">
                          <div className="flex flex-col items-center gap-2">
                            <FileText className="h-8 w-8 text-slate-300" />
                            <span>Tidak ada data pengajuan</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      pengajuan_list.data.map((pengajuan) => (
                        <TableRow key={pengajuan.id} className="border-b border-slate-50 hover:bg-slate-50/50 transition-colors">
                          <TableCell className="font-mono text-slate-600 py-4">{pengajuan.nip}</TableCell>
                          <TableCell className="font-medium text-slate-800 py-4">
                            {pengajuan.pegawai?.nama || 'N/A'}
                          </TableCell>
                          <TableCell className="text-slate-600 py-4">{getJenisPensiunLabel(pengajuan.jenis_pensiun)}</TableCell>
                          <TableCell className="text-slate-600 py-4">{formatDate(pengajuan.created_at)}</TableCell>
                          <TableCell className="py-4">
                            <Badge variant={getStatusBadgeVariant(pengajuan.status)} className="font-medium">
                              {getStatusLabel(pengajuan.status)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-sm text-slate-500 py-4">
                            {pengajuan.user?.name || 'N/A'}
                          </TableCell>
                          <TableCell className="text-right py-4">
                            <div className="flex justify-end gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.location.href = `/kanwil/pengajuan-kelola/${pengajuan.id}`}
                                className="gap-1 border-slate-200 text-slate-600 hover:bg-slate-50 hover:text-slate-800"
                              >
                                <Eye className="h-4 w-4" />
                                Detail
                              </Button>
                              
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.location.href = `/kanwil/pengajuan-kelola/${pengajuan.id}/edit`}
                                className="gap-1 border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
                              >
                                <Edit className="h-4 w-4" />
                                Edit
                              </Button>
                              
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDelete(pengajuan.id)}
                                className="gap-1 border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                                Hapus
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
              
              {pengajuan_list.data.length > 0 && (
                <div className="flex items-center justify-between px-6 py-4 border-t border-slate-100">
                  <div className="text-sm text-slate-500">
                    Menampilkan {pengajuan_list.data.length} dari {pengajuan_list.total} pengajuan
                  </div>
                  <div className="flex items-center space-x-2">
                    {pengajuan_list.current_page > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.href = `/kanwil/pengajuan-kelola?page=${pengajuan_list.current_page - 1}`}
                        className="border-slate-200 text-slate-600 hover:bg-slate-50"
                      >
                        Previous
                      </Button>
                    )}
                    <span className="text-sm text-slate-500">
                      Page {pengajuan_list.current_page} of {pengajuan_list.last_page}
                    </span>
                    {pengajuan_list.current_page < pengajuan_list.last_page && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.href = `/kanwil/pengajuan-kelola?page=${pengajuan_list.current_page + 1}`}
                        className="border-slate-200 text-slate-600 hover:bg-slate-50"
                      >
                        Next
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    </>
  )
}
