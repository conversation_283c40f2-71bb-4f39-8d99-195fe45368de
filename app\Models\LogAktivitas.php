<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LogAktivitas extends Model
{
    protected $table = 'log_aktivitas';
    
    protected $fillable = [
        'user_id',
        'aksi',
        'entitas',
        'entitas_id',
        'detail',
        'data_lama',
        'data_baru',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'data_lama' => 'array',
        'data_baru' => 'array',
    ];

    // Relationship dengan User
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scope untuk entitas tertentu
    public function scopeEntitas($query, $entitas)
    {
        return $query->where('entitas', $entitas);
    }

    // Scope untuk user tertentu
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    // Scope untuk periode tertentu
    public function scopePeriode($query, $start, $end)
    {
        return $query->whereBetween('created_at', [$start, $end]);
    }

    // Static method untuk log aktivitas
    public static function logActivity($aksi, $entitas, $entitasId = null, $detail = null, $dataLama = null, $dataBaru = null)
    {
        if (!auth()->check()) {
            return;
        }

        return static::create([
            'user_id' => auth()->id(),
            'aksi' => $aksi,
            'entitas' => $entitas,
            'entitas_id' => $entitasId,
            'detail' => $detail,
            'data_lama' => $dataLama,
            'data_baru' => $dataBaru,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    // Get description yang readable
    public function getDescriptionAttribute()
    {
        $userName = $this->user->name ?? 'Unknown User';
        $detail = $this->detail ? " - {$this->detail}" : '';
        
        return "{$userName} {$this->aksi} {$this->entitas}" . ($this->entitas_id ? " (ID: {$this->entitas_id})" : '') . $detail;
    }
}
