<?php

use App\Http\Controllers\Api\PegawaiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Public API endpoints for external employee data
Route::prefix('public')->group(function () {
    Route::get('/employees', function () {
        // Mock data untuk testing - ganti dengan API external yang sebenarnya
        return response()->json([
            'pegawai' => [
                [
                    'nip' => '198501012010011001',
                    'nama' => 'Ahmad Rival',
                    'golongan' => 'III/a',
                    'tmt_pensiun' => '2025-01-01T00:00:00.000Z',
                    'unit_kerja' => 'Bidang Mutasi',
                    'induk_unit' => 'Kantor Wilayah',
                    'jabatan' => 'Kepala Seksi',
                    'kantor_id' => '550e8400-e29b-41d4-a716-446655440000',
                    'jenis_pegawai' => 'pegawai',
                    'aktif' => true
                ],
                [
                    'nip' => '198502012010011002',
                    'nama' => 'Siti Aminah',
                    'golongan' => 'II/d',
                    'tmt_pensiun' => '2024-12-15T00:00:00.000Z',
                    'unit_kerja' => 'Bidang Kepegawaian',
                    'induk_unit' => 'Kantor Wilayah',
                    'jabatan' => 'Staff',
                    'kantor_id' => '550e8400-e29b-41d4-a716-446655440000',
                    'jenis_pegawai' => 'pegawai',
                    'aktif' => true
                ]
            ]
        ]);
    });
    
    Route::get('/employees/{nip}', function ($nip) {
        // Mock single employee data
        return response()->json([
            'pegawai' => [
                'nip' => $nip,
                'nama' => 'Ahmad Rival',
                'golongan' => 'III/a',
                'tmt_pensiun' => '2025-01-01T00:00:00.000Z',
                'unit_kerja' => 'Bidang Mutasi',
                'induk_unit' => 'Kantor Wilayah',
                'jabatan' => 'Kepala Seksi',
                'kantor_id' => '550e8400-e29b-41d4-a716-446655440000',
                'jenis_pegawai' => 'pegawai',
                'aktif' => true
            ]
        ]);
    });
});

// Protected API routes
Route::middleware(['auth:sanctum'])->group(function () {
    
    // Routes untuk pegawai data
    Route::get('/pegawai', [PegawaiController::class, 'index']);
    Route::get('/pegawai/{nip}', [PegawaiController::class, 'show']);
    Route::post('/pegawai/sync', [PegawaiController::class, 'sync']);
    Route::get('/pegawai/stats', [PegawaiController::class, 'stats']);
    
    // Routes khusus Kabupaten (Operator)
    Route::middleware('role:Kabupaten')->prefix('kabupaten')->group(function () {
        Route::get('/pegawai-tmt', [PegawaiController::class, 'tmtHariIni']);
        Route::post('/pengajuan', function () {
            return response()->json(['message' => 'Buat pengajuan pensiun']);
        });
        Route::put('/pengajuan/{id}', function ($id) {
            return response()->json(['message' => "Update pengajuan {$id}"]);
        });
    });
    
    // Routes khusus Kanwil (Superadmin)
    Route::middleware('role:Kanwil')->prefix('kanwil')->group(function () {
        Route::get('/pengajuan', function () {
            return response()->json(['message' => 'Semua pengajuan untuk Kanwil']);
        });
        Route::post('/pengajuan/{id}/approve', function ($id) {
            return response()->json(['message' => "Setujui pengajuan {$id}"]);
        });
        Route::post('/pengajuan/{id}/reject', function ($id) {
            return response()->json(['message' => "Tolak pengajuan {$id}"]);
        });
    });
    
    // Routes khusus Admin Pusat (Viewer)
    Route::middleware('role:Admin Pusat')->prefix('admin-pusat')->group(function () {
        Route::get('/pengajuan', function () {
            return response()->json(['message' => 'View semua pengajuan untuk Admin Pusat']);
        });
        Route::get('/statistik', function () {
            return response()->json(['message' => 'Statistik pengajuan']);
        });
    });
});
