import { Head } from '@inertiajs/react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { 
  Search, 
  Eye, 
  FileText, 
  Users, 
  Clock, 
  TrendingUp, 
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { useState } from 'react'
import { formatDate, getStatusBadgeVariant, getStatusLabel, getJenisPensiunLabel } from '@/lib/utils'
import { PengajuanPensiun } from '@/types'

interface DashboardStats {
  total_pengajuan: number
  total_disetujui: number
  total_ditolak: number
  menunggu_review: number
  bulan_ini: number
  tahun_ini: number
  rata_waktu_approval: number | null
}

interface CacheStats {
  total_pegawai: number
  last_sync: string | null
  cache_size: string
}

interface Props {
  pengajuan_list: {
    data: PengajuanPensiun[]
    current_page: number
    last_page: number
    total: number
  }
  statistik: DashboardStats
  statistik_bulanan: Record<string, any[]>
  statistik_jenis: Record<string, any[]>
  cache_stats: CacheStats
  filters: {
    status?: string
    jenis_pensiun?: string
    periode?: string
  }
  status_options: string[]
  jenis_pensiun_options: string[]
  periode_options: Record<string, string>
}

export default function AdminPusatDashboard({ 
  pengajuan_list, 
  statistik, 
  statistik_bulanan,
  statistik_jenis,
  cache_stats,
  filters, 
  status_options, 
  jenis_pensiun_options,
  periode_options
}: Props) {
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState(filters.status || 'all')
  const [jenisFilter, setJenisFilter] = useState(filters.jenis_pensiun || 'all')
  const [periodeFilter, setPeriodeFilter] = useState(filters.periode || 'all')

  const handleFilterChange = () => {
    const params = new URLSearchParams()
    if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter)
    if (jenisFilter && jenisFilter !== 'all') params.append('jenis_pensiun', jenisFilter)
    if (periodeFilter && periodeFilter !== 'all') params.append('periode', periodeFilter)
    if (search) params.append('search', search)

    window.location.href = `/admin-pusat/dashboard?${params.toString()}`
  }

  const handleResetFilter = () => {
    setStatusFilter('all')
    setJenisFilter('all')
    setPeriodeFilter('all')
    setSearch('')
    window.location.href = '/admin-pusat/dashboard'
  }

  const tingkatApproval = statistik.total_pengajuan > 0 
    ? ((statistik.total_disetujui / statistik.total_pengajuan) * 100).toFixed(1)
    : '0.0'

  return (
    <AppLayout title="Dashboard Admin Pusat">
      <div className="space-y-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-800 mb-2">Dashboard Admin Pusat</h1>
          <p className="text-slate-600">Monitoring dan analisis pengajuan pensiun ASN</p>
        </div>

        {/* Main Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Total Pengajuan</CardTitle>
              <div className="p-2 bg-blue-50 rounded-lg">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-800 mb-1">{statistik.total_pengajuan}</div>
              <p className="text-xs text-slate-500">
                {statistik.bulan_ini} bulan ini
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Tingkat Approval</CardTitle>
              <div className="p-2 bg-emerald-50 rounded-lg">
                <TrendingUp className="h-4 w-4 text-emerald-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-emerald-600 mb-1">{tingkatApproval}%</div>
              <p className="text-xs text-slate-500">
                {statistik.total_disetujui} disetujui
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Menunggu Review</CardTitle>
              <div className="p-2 bg-orange-50 rounded-lg">
                <Clock className="h-4 w-4 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600 mb-1">{statistik.menunggu_review}</div>
              <p className="text-xs text-slate-500">
                Perlu ditindaklanjuti
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Rata-rata Approval</CardTitle>
              <div className="p-2 bg-purple-50 rounded-lg">
                <Calendar className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-800 mb-1">
                {statistik.rata_waktu_approval ? Math.round(statistik.rata_waktu_approval) : 0} hari
              </div>
              <p className="text-xs text-slate-500">
                Waktu proses
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Status Breakdown */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Disetujui</CardTitle>
              <div className="p-2 bg-emerald-50 rounded-lg">
                <CheckCircle className="h-4 w-4 text-emerald-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-emerald-600 mb-1">{statistik.total_disetujui}</div>
              <p className="text-xs text-slate-500">
                Pengajuan berhasil
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Ditolak</CardTitle>
              <div className="p-2 bg-red-50 rounded-lg">
                <XCircle className="h-4 w-4 text-red-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600 mb-1">{statistik.total_ditolak}</div>
              <p className="text-xs text-slate-500">
                Pengajuan ditolak
              </p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Cache Pegawai</CardTitle>
              <div className="p-2 bg-indigo-50 rounded-lg">
                <Users className="h-4 w-4 text-indigo-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-800 mb-1">{cache_stats.total_pegawai}</div>
              <p className="text-xs text-slate-500">
                {cache_stats.last_sync ? `Sync: ${formatDate(cache_stats.last_sync)}` : 'Belum sync'}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Statistik per Jenis Pensiun */}
        {Object.keys(statistik_jenis).length > 0 && (
          <Card className="border-0 shadow-sm bg-white mb-8">
            <CardHeader className="border-b border-slate-100 bg-slate-50/50">
              <CardTitle className="text-lg font-semibold text-slate-800">Statistik per Jenis Pensiun</CardTitle>
              <CardDescription className="text-slate-600">Distribusi pengajuan berdasarkan jenis pensiun</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(statistik_jenis).map(([jenis, data]) => {
                  const total = (data as any[]).reduce((sum, item) => sum + item.total, 0)
                  const disetujui = (data as any[]).find(item => item.status === 'Disetujui')?.total || 0

                  return (
                    <div key={jenis} className="p-4 border border-slate-100 rounded-xl bg-slate-50/30 hover:bg-slate-50/50 transition-colors">
                      <div className="text-sm font-medium text-slate-600">{getJenisPensiunLabel(jenis)}</div>
                      <div className="text-2xl font-bold text-slate-800">{total}</div>
                      <div className="text-xs text-slate-500">
                        {disetujui} disetujui
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pengajuan Table */}
        <Card className="border-0 shadow-sm bg-white">
          <CardHeader className="border-b border-slate-100 bg-slate-50/50">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-slate-800">Daftar Pengajuan Pensiun</CardTitle>
                <CardDescription className="text-slate-600">
                  Monitoring semua pengajuan pensiun di sistem
                </CardDescription>
              </div>
              <Button
                variant="outline"
                onClick={() => window.location.href = '/admin-pusat/statistik'}
                className="border-slate-200 text-slate-600 hover:bg-slate-50"
              >
                Lihat Statistik Detail
              </Button>
            </div>

            {/* Filters */}
            <div className="flex flex-wrap items-center gap-4 pt-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Cari pegawai..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                />
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px] border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  {status_options.map((status) => (
                    <SelectItem key={status} value={status}>
                      {getStatusLabel(status)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={jenisFilter} onValueChange={setJenisFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Semua Jenis" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Jenis</SelectItem>
                  {jenis_pensiun_options.map((jenis) => (
                    <SelectItem key={jenis} value={jenis}>
                      {getJenisPensiunLabel(jenis)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={periodeFilter} onValueChange={setPeriodeFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Semua Periode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Periode</SelectItem>
                  {Object.entries(periode_options).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button onClick={handleFilterChange}>Filter</Button>
              <Button variant="outline" onClick={handleResetFilter}>Reset</Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>NIP</TableHead>
                    <TableHead>Nama Pegawai</TableHead>
                    <TableHead>Jenis Pensiun</TableHead>
                    <TableHead>Tanggal Pengajuan</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Operator</TableHead>
                    <TableHead>Penyetuju</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pengajuan_list.data.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                        Tidak ada data pengajuan
                      </TableCell>
                    </TableRow>
                  ) : (
                    pengajuan_list.data.map((pengajuan) => (
                      <TableRow key={pengajuan.id}>
                        <TableCell className="font-mono">{pengajuan.nip}</TableCell>
                        <TableCell className="font-medium">
                          {pengajuan.pegawai?.nama || 'N/A'}
                        </TableCell>
                        <TableCell>{getJenisPensiunLabel(pengajuan.jenis_pensiun)}</TableCell>
                        <TableCell>{formatDate(pengajuan.created_at)}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(pengajuan.status)}>
                            {getStatusLabel(pengajuan.status)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {pengajuan.user?.name || 'N/A'}
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {pengajuan.penyetuju?.name || 
                           (pengajuan.status === 'Disetujui' || pengajuan.status === 'Ditolak' ? 'N/A' : '-')}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.location.href = `/admin-pusat/pengajuan/${pengajuan.id}`}
                            className="gap-1"
                          >
                            <Eye className="h-4 w-4" />
                            Detail
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
            
            {pengajuan_list.data.length > 0 && (
              <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                <div>
                  Menampilkan {pengajuan_list.data.length} dari {pengajuan_list.total} pengajuan
                </div>
                <div className="flex gap-2">
                  {pengajuan_list.current_page > 1 && (
                    <Button variant="outline" size="sm">
                      Previous
                    </Button>
                  )}
                  {pengajuan_list.current_page < pengajuan_list.last_page && (
                    <Button variant="outline" size="sm">
                      Next
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
