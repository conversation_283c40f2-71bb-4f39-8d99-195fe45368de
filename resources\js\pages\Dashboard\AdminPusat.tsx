import { Head } from '@inertiajs/react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { 
  Search, 
  Eye, 
  FileText, 
  Users, 
  Clock, 
  TrendingUp, 
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { useState } from 'react'
import { formatDate, getStatusBadgeVariant, getStatusLabel, getJenisPensiunLabel } from '@/lib/utils'
import { PengajuanPensiun } from '@/types'

interface DashboardStats {
  total_pengajuan: number
  total_disetujui: number
  total_ditolak: number
  menunggu_review: number
  bulan_ini: number
  tahun_ini: number
  rata_waktu_approval: number | null
}

interface CacheStats {
  total_pegawai: number
  last_sync: string | null
  cache_size: string
}

interface Props {
  pengajuan_list: {
    data: PengajuanPensiun[]
    current_page: number
    last_page: number
    total: number
  }
  statistik: DashboardStats
  statistik_bulanan: Record<string, any[]>
  statistik_jenis: Record<string, any[]>
  cache_stats: CacheStats
  filters: {
    status?: string
    jenis_pensiun?: string
    periode?: string
  }
  status_options: string[]
  jenis_pensiun_options: string[]
  periode_options: Record<string, string>
}

export default function AdminPusatDashboard({ 
  pengajuan_list, 
  statistik, 
  statistik_bulanan,
  statistik_jenis,
  cache_stats,
  filters, 
  status_options, 
  jenis_pensiun_options,
  periode_options
}: Props) {
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState(filters.status || 'all')
  const [jenisFilter, setJenisFilter] = useState(filters.jenis_pensiun || 'all')
  const [periodeFilter, setPeriodeFilter] = useState(filters.periode || 'all')

  const handleFilterChange = () => {
    const params = new URLSearchParams()
    if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter)
    if (jenisFilter && jenisFilter !== 'all') params.append('jenis_pensiun', jenisFilter)
    if (periodeFilter && periodeFilter !== 'all') params.append('periode', periodeFilter)
    if (search) params.append('search', search)

    window.location.href = `/admin-pusat/dashboard?${params.toString()}`
  }

  const handleResetFilter = () => {
    setStatusFilter('all')
    setJenisFilter('all')
    setPeriodeFilter('all')
    setSearch('')
    window.location.href = '/admin-pusat/dashboard'
  }

  const tingkatApproval = statistik.total_pengajuan > 0 
    ? ((statistik.total_disetujui / statistik.total_pengajuan) * 100).toFixed(1)
    : '0.0'

  return (
    <AppLayout title="Dashboard Admin Pusat">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard Admin Pusat</h1>
          <p className="text-gray-600">Monitoring dan analisis pengajuan pensiun ASN</p>
        </div>

        {/* Main Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Pengajuan</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistik.total_pengajuan}</div>
              <p className="text-xs text-muted-foreground">
                {statistik.bulan_ini} bulan ini
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tingkat Approval</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{tingkatApproval}%</div>
              <p className="text-xs text-muted-foreground">
                {statistik.total_disetujui} disetujui
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Menunggu Review</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-600">{statistik.menunggu_review}</div>
              <p className="text-xs text-muted-foreground">
                Perlu ditindaklanjuti
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Rata-rata Approval</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {statistik.rata_waktu_approval ? Math.round(statistik.rata_waktu_approval) : 0} hari
              </div>
              <p className="text-xs text-muted-foreground">
                Waktu proses
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Status Breakdown */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Disetujui</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{statistik.total_disetujui}</div>
              <p className="text-xs text-muted-foreground">
                Pengajuan berhasil
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ditolak</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{statistik.total_ditolak}</div>
              <p className="text-xs text-muted-foreground">
                Pengajuan ditolak
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cache Pegawai</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{cache_stats.total_pegawai}</div>
              <p className="text-xs text-muted-foreground">
                {cache_stats.last_sync ? `Sync: ${formatDate(cache_stats.last_sync)}` : 'Belum sync'}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Statistik per Jenis Pensiun */}
        {Object.keys(statistik_jenis).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Statistik per Jenis Pensiun</CardTitle>
              <CardDescription>Distribusi pengajuan berdasarkan jenis pensiun</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(statistik_jenis).map(([jenis, data]) => {
                  const total = (data as any[]).reduce((sum, item) => sum + item.total, 0)
                  const disetujui = (data as any[]).find(item => item.status === 'Disetujui')?.total || 0
                  
                  return (
                    <div key={jenis} className="p-4 border rounded-lg">
                      <div className="text-sm font-medium text-gray-600">{getJenisPensiunLabel(jenis)}</div>
                      <div className="text-2xl font-bold">{total}</div>
                      <div className="text-xs text-muted-foreground">
                        {disetujui} disetujui
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Pengajuan Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Daftar Pengajuan Pensiun</CardTitle>
                <CardDescription>
                  Monitoring semua pengajuan pensiun di sistem
                </CardDescription>
              </div>
              <Button
                variant="outline"
                onClick={() => window.location.href = '/admin-pusat/statistik'}
              >
                Lihat Statistik Detail
              </Button>
            </div>
            
            {/* Filters */}
            <div className="flex flex-wrap items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari pegawai..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  {status_options.map((status) => (
                    <SelectItem key={status} value={status}>
                      {getStatusLabel(status)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={jenisFilter} onValueChange={setJenisFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Semua Jenis" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Jenis</SelectItem>
                  {jenis_pensiun_options.map((jenis) => (
                    <SelectItem key={jenis} value={jenis}>
                      {getJenisPensiunLabel(jenis)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={periodeFilter} onValueChange={setPeriodeFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Semua Periode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Periode</SelectItem>
                  {Object.entries(periode_options).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button onClick={handleFilterChange}>Filter</Button>
              <Button variant="outline" onClick={handleResetFilter}>Reset</Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>NIP</TableHead>
                    <TableHead>Nama Pegawai</TableHead>
                    <TableHead>Jenis Pensiun</TableHead>
                    <TableHead>Tanggal Pengajuan</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Operator</TableHead>
                    <TableHead>Penyetuju</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pengajuan_list.data.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                        Tidak ada data pengajuan
                      </TableCell>
                    </TableRow>
                  ) : (
                    pengajuan_list.data.map((pengajuan) => (
                      <TableRow key={pengajuan.id}>
                        <TableCell className="font-mono">{pengajuan.nip}</TableCell>
                        <TableCell className="font-medium">
                          {pengajuan.pegawai?.nama || 'N/A'}
                        </TableCell>
                        <TableCell>{getJenisPensiunLabel(pengajuan.jenis_pensiun)}</TableCell>
                        <TableCell>{formatDate(pengajuan.created_at)}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(pengajuan.status)}>
                            {getStatusLabel(pengajuan.status)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {pengajuan.user?.name || 'N/A'}
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {pengajuan.penyetuju?.name || 
                           (pengajuan.status === 'Disetujui' || pengajuan.status === 'Ditolak' ? 'N/A' : '-')}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.location.href = `/admin-pusat/pengajuan/${pengajuan.id}`}
                            className="gap-1"
                          >
                            <Eye className="h-4 w-4" />
                            Detail
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
            
            {pengajuan_list.data.length > 0 && (
              <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                <div>
                  Menampilkan {pengajuan_list.data.length} dari {pengajuan_list.total} pengajuan
                </div>
                <div className="flex gap-2">
                  {pengajuan_list.current_page > 1 && (
                    <Button variant="outline" size="sm">
                      Previous
                    </Button>
                  )}
                  {pengajuan_list.current_page < pengajuan_list.last_page && (
                    <Button variant="outline" size="sm">
                      Next
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
