<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('log_aktivitas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('aksi'); // Jenis aksi yang dilakukan
            $table->string('entitas'); // Entitas yang dipengaruhi (pengajuan, dokumen, etc)
            $table->unsignedBigInteger('entitas_id')->nullable(); // ID entitas
            $table->text('detail')->nullable(); // Detail tambahan aksi
            $table->json('data_lama')->nullable(); // Data sebelum perubahan
            $table->json('data_baru')->nullable(); // Data setelah perubahan
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'created_at']);
            $table->index(['entitas', 'entitas_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('log_aktivitas');
    }
};
