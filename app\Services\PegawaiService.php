<?php

namespace App\Services;

use App\Models\PegawaiCache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PegawaiService
{
    private string $apiBaseUrl;

    public function __construct()
    {
        // API endpoint di port 3001
        $this->apiBaseUrl = 'http://localhost:3001/api/public';
    }

    /**
     * Sync semua data pegawai dari API external menggunakan endpoint /all
     */
    public function syncAllPegawai(): array
    {
        try {
            $response = Http::timeout(60)->get("{$this->apiBaseUrl}/employees/all");
            
            if (!$response->successful()) {
                throw new \Exception("API Error: " . $response->status());
            }

            $data = $response->json();
            $pegawaiList = $data['data'] ?? [];
            
            Log::info("Starting sync for all employees", [
                'total_data' => count($pegawaiList)
            ]);

            $synced = 0;
            $errors = [];

            foreach ($pegawaiList as $pegawaiData) {
                try {
                    $this->syncSinglePegawai($pegawaiData);
                    $synced++;
                    
                    // Log progress every 100 records
                    if ($synced % 100 === 0) {
                        Log::info("Sync progress: {$synced} records processed");
                    }
                } catch (\Exception $e) {
                    $nip = $pegawaiData['nip'] ?? 'unknown';
                    $errors[] = "Error syncing NIP {$nip}: " . $e->getMessage();
                    Log::error("Error syncing pegawai", [
                        'nip' => $pegawaiData['nip'] ?? null,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info("Pegawai sync completed", [
                'total' => count($pegawaiList),
                'synced' => $synced,
                'errors' => count($errors)
            ]);

            return [
                'success' => true,
                'total' => count($pegawaiList),
                'synced' => $synced,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Log::error("Failed to sync pegawai from API", ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync single pegawai data
     */
    private function syncSinglePegawai(array $pegawaiData): void
    {
        // Ambil semua data dari API tanpa filter
        $kantorId = $pegawaiData['kantor_id'] ?? null;
        
        // Parse TMT pensiun dengan handle berbagai format
        $tmtPensiun = null;
        try {
            if (isset($pegawaiData['tmt_pensiun']) && !empty($pegawaiData['tmt_pensiun'])) {
                // Coba parsing dengan format dari API (YYYY-MM-DD HH:mm:ss)
                $tmtString = $pegawaiData['tmt_pensiun'];
                
                // Log format yang diterima untuk debugging
                Log::info("Parsing TMT pensiun", [
                    'nip' => $pegawaiData['nip'],
                    'tmt_raw' => $tmtString
                ]);
                
                // Parse dengan Carbon
                $tmtPensiun = Carbon::createFromFormat('Y-m-d H:i:s', $tmtString);
                
                if (!$tmtPensiun) {
                    // Fallback ke parse otomatis
                    $tmtPensiun = Carbon::parse($tmtString);
                }
            }
        } catch (\Exception $e) {
            Log::warning("Invalid TMT pensiun format for NIP {$pegawaiData['nip']}: {$pegawaiData['tmt_pensiun']}", [
                'error' => $e->getMessage()
            ]);
            $tmtPensiun = null;
        }

        PegawaiCache::updateOrCreate(
            ['nip' => $pegawaiData['nip']],
            [
                'nama' => $pegawaiData['nama'] ?? '',
                'golongan' => $pegawaiData['golongan'] ?? '',
                'tmt_pensiun' => $tmtPensiun,
                'unit_kerja' => $pegawaiData['unit_kerja'] ?? '',
                'induk_unit' => $pegawaiData['unit_kerja'] ?? '', // Gunakan unit_kerja sebagai induk_unit
                'jabatan' => $pegawaiData['jabatan'] ?? '',
                'kantor_id' => $kantorId,
                'jenis_pegawai' => 'pegawai', // Default value
                'aktif' => true, // Default aktif
                'last_sync' => now()
            ]
        );
    }

    /**
     * Get pegawai by NIP dari API external jika tidak ada di cache
     */
    public function getPegawaiByNip(string $nip): ?array
    {
        try {
            // Cek cache dulu
            $cachedPegawai = PegawaiCache::where('nip', $nip)->first();
            
            if ($cachedPegawai && $cachedPegawai->last_sync->isAfter(now()->subHours(6))) {
                return $cachedPegawai->toArray();
            }

            // Ambil dari API jika cache expired atau tidak ada
            $response = Http::timeout(10)->get("{$this->apiBaseUrl}/employees/{$nip}");
            
            if (!$response->successful()) {
                return $cachedPegawai?->toArray();
            }

            $data = $response->json();
            $pegawaiData = $data['pegawai'] ?? null;

            if ($pegawaiData) {
                $this->syncSinglePegawai($pegawaiData);
                return $pegawaiData;
            }

            return $cachedPegawai?->toArray();

        } catch (\Exception $e) {
            Log::error("Failed to get pegawai by NIP", [
                'nip' => $nip,
                'error' => $e->getMessage()
            ]);
            
            // Return cache data if API fails
            return PegawaiCache::where('nip', $nip)->first()?->toArray();
        }
    }

    /**
     * Get pegawai yang sudah pensiun (untuk dibuatkan pengajuan berkas)
     */
    public function getPegawaiSudahPensiun(): \Illuminate\Database\Eloquent\Collection
    {
        return PegawaiCache::sudahPensiun()->orderBy('tmt_pensiun')->get();
    }
    
    /**
     * Alias untuk backward compatibility
     */
    public function getPegawaiTmtHariIni(): \Illuminate\Database\Eloquent\Collection
    {
        return $this->getPegawaiSudahPensiun();
    }

    /**
     * Check apakah data cache sudah expired
     */
    public function isCacheExpired(): bool
    {
        $lastSync = PegawaiCache::max('last_sync');
        
        if (!$lastSync) {
            return true;
        }

        return Carbon::parse($lastSync)->isAfter(now()->subHours(24));
    }

    /**
     * Get statistik cache
     */
    public function getCacheStats(): array
    {
        return [
            'total_pegawai' => PegawaiCache::count(),
            'pegawai_aktif' => PegawaiCache::aktif()->count(),
            'pegawai_tmt_hari_ini' => PegawaiCache::tmtPensiunHariIni()->count(),
            'last_sync' => PegawaiCache::max('last_sync'),
            'cache_expired' => $this->isCacheExpired()
        ];
    }
}
