<?php

namespace App\Services;

use App\Models\PegawaiCache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PegawaiService
{
    private string $apiBaseUrl;

    public function __construct()
    {
        // Untuk testing, gunakan localhost
        $this->apiBaseUrl = 'http://localhost:3001/api/public';
    }

    /**
     * Sync semua data pegawai dari API external
     */
    public function syncAllPegawai(): array
    {
        try {
            $response = Http::timeout(30)->get("{$this->apiBaseUrl}/employees");
            
            if (!$response->successful()) {
                throw new \Exception("API Error: " . $response->status());
            }

            $data = $response->json();
            $pegawaiList = $data['pegawai'] ?? [];
            
            $synced = 0;
            $errors = [];

            foreach ($pegawaiList as $pegawaiData) {
                try {
                    $this->syncSinglePegawai($pegawaiData);
                    $synced++;
                } catch (\Exception $e) {
                    $nip = $pegawaiData['nip'] ?? 'unknown';
                    $errors[] = "Error syncing NIP {$nip}: " . $e->getMessage();
                    Log::error("Error syncing pegawai", [
                        'nip' => $pegawaiData['nip'] ?? null,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info("Pegawai sync completed", [
                'total' => count($pegawaiList),
                'synced' => $synced,
                'errors' => count($errors)
            ]);

            return [
                'success' => true,
                'total' => count($pegawaiList),
                'synced' => $synced,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Log::error("Failed to sync pegawai from API", ['error' => $e->getMessage()]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync single pegawai data
     */
    private function syncSinglePegawai(array $pegawaiData): void
    {
        PegawaiCache::updateOrCreate(
            ['nip' => $pegawaiData['nip']],
            [
                'nama' => $pegawaiData['nama'],
                'golongan' => $pegawaiData['golongan'],
                'tmt_pensiun' => Carbon::parse($pegawaiData['tmt_pensiun']),
                'unit_kerja' => $pegawaiData['unit_kerja'],
                'induk_unit' => $pegawaiData['induk_unit'],
                'jabatan' => $pegawaiData['jabatan'],
                // kantor_id removed
                'jenis_pegawai' => $pegawaiData['jenis_pegawai'],
                'aktif' => $pegawaiData['aktif'] ?? true,
                'last_sync' => now()
            ]
        );
    }

    /**
     * Get pegawai by NIP dari API external jika tidak ada di cache
     */
    public function getPegawaiByNip(string $nip): ?array
    {
        try {
            // Cek cache dulu
            $cachedPegawai = PegawaiCache::where('nip', $nip)->first();
            
            if ($cachedPegawai && $cachedPegawai->last_sync->isAfter(now()->subHours(6))) {
                return $cachedPegawai->toArray();
            }

            // Ambil dari API jika cache expired atau tidak ada
            $response = Http::timeout(10)->get("{$this->apiBaseUrl}/employees/{$nip}");
            
            if (!$response->successful()) {
                return $cachedPegawai?->toArray();
            }

            $data = $response->json();
            $pegawaiData = $data['pegawai'] ?? null;

            if ($pegawaiData) {
                $this->syncSinglePegawai($pegawaiData);
                return $pegawaiData;
            }

            return $cachedPegawai?->toArray();

        } catch (\Exception $e) {
            Log::error("Failed to get pegawai by NIP", [
                'nip' => $nip,
                'error' => $e->getMessage()
            ]);
            
            // Return cache data if API fails
            return PegawaiCache::where('nip', $nip)->first()?->toArray();
        }
    }

    /**
     * Get pegawai yang TMT pensiun <= hari ini
     */
    public function getPegawaiTmtHariIni(): \Illuminate\Database\Eloquent\Collection
    {
        return PegawaiCache::tmtPensiunHariIni()->aktif()->orderBy('tmt_pensiun')->get();
    }

    /**
     * Check apakah data cache sudah expired
     */
    public function isCacheExpired(): bool
    {
        $lastSync = PegawaiCache::max('last_sync');
        
        if (!$lastSync) {
            return true;
        }

        return Carbon::parse($lastSync)->isAfter(now()->subHours(24));
    }

    /**
     * Get statistik cache
     */
    public function getCacheStats(): array
    {
        return [
            'total_pegawai' => PegawaiCache::count(),
            'pegawai_aktif' => PegawaiCache::aktif()->count(),
            'pegawai_tmt_hari_ini' => PegawaiCache::tmtPensiunHariIni()->count(),
            'last_sync' => PegawaiCache::max('last_sync'),
            'cache_expired' => $this->isCacheExpired()
        ];
    }
}
