import { Head } from '@inertiajs/react'
import { useState } from 'react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Search, Eye, Users, Sync, Calendar, Building } from 'lucide-react'
import { formatDate } from '@/lib/utils'

interface PegawaiCache {
  nip: string
  nama: string
  jabatan: string
  unit_kerja: string
  status_kepegawaian: string
  tmt_pensiun: string
  jenis_kelamin: string
  tempat_lahir: string
  tanggal_lahir: string
}

interface CacheStats {
  total_pegawai: number
  last_sync: string | null
}

interface Props {
  pegawai_list: {
    data: PegawaiCache[]
    current_page: number
    last_page: number
    total: number
  }
  unit_kerja_options: string[]
  cache_stats: CacheStats
  filters: {
    search?: string
    unit_kerja?: string
    status_kepegawaian?: string
    tmt_filter?: string
  }
  status_kepegawaian_options: string[]
  tmt_filter_options: Record<string, string>
}

export default function PegawaiIndex({ 
  pegawai_list, 
  unit_kerja_options, 
  cache_stats, 
  filters, 
  status_kepegawaian_options, 
  tmt_filter_options 
}: Props) {
  const [search, setSearch] = useState(filters.search || '')
  const [unitKerja, setUnitKerja] = useState(filters.unit_kerja || 'all')
  const [statusKepegawaian, setStatusKepegawaian] = useState(filters.status_kepegawaian || 'all')
  const [tmtFilter, setTmtFilter] = useState(filters.tmt_filter || 'all')

  const handleFilterChange = () => {
    const params = new URLSearchParams()
    if (search) params.append('search', search)
    if (unitKerja && unitKerja !== 'all') params.append('unit_kerja', unitKerja)
    if (statusKepegawaian && statusKepegawaian !== 'all') params.append('status_kepegawaian', statusKepegawaian)
    if (tmtFilter && tmtFilter !== 'all') params.append('tmt_filter', tmtFilter)
    
    window.location.href = `/kanwil/pegawai?${params.toString()}`
  }

  const handleResetFilter = () => {
    setSearch('')
    setUnitKerja('all')
    setStatusKepegawaian('all')
    setTmtFilter('all')
    window.location.href = '/kanwil/pegawai'
  }

  const handleSync = async () => {
    try {
      const response = await fetch('/kanwil/pegawai/sync', {
        method: 'POST',
        headers: {
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'Content-Type': 'application/json'
        }
      })
      
      const result = await response.json()
      if (result.success) {
        alert('Data pegawai berhasil disinkronisasi')
        window.location.reload()
      } else {
        alert('Gagal sinkronisasi: ' + result.message)
      }
    } catch (error) {
      alert('Terjadi kesalahan saat sinkronisasi')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PNS':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">PNS</Badge>
      case 'PPPK':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">PPPK</Badge>
      case 'Honorer':
        return <Badge variant="outline" className="border-orange-200 text-orange-800">Honorer</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <>
      <Head title="Data Pegawai - Kanwil" />
      <AppLayout title="Data Pegawai">
        <div className="space-y-8 pb-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-slate-800 mb-2">Data Pegawai</h1>
            <p className="text-slate-600">Kelola data pegawai dan informasi kepegawaian</p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Total Pegawai</CardTitle>
                <div className="p-2 bg-blue-50 rounded-lg">
                  <Users className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-slate-800 mb-1">{cache_stats.total_pegawai}</div>
                <p className="text-xs text-slate-500">Pegawai terdaftar</p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Last Sync</CardTitle>
                <div className="p-2 bg-emerald-50 rounded-lg">
                  <Sync className="h-4 w-4 text-emerald-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-sm font-medium text-slate-800 mb-1">
                  {cache_stats.last_sync ? formatDate(cache_stats.last_sync) : 'Belum pernah'}
                </div>
                <p className="text-xs text-slate-500">Sinkronisasi terakhir</p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-slate-700">Aksi</CardTitle>
                <div className="p-2 bg-purple-50 rounded-lg">
                  <Calendar className="h-4 w-4 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={handleSync}
                  size="sm"
                  className="bg-purple-600 hover:bg-purple-700 text-white shadow-sm"
                >
                  <Sync className="h-4 w-4 mr-2" />
                  Sync Data
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Pegawai Table */}
          <Card className="border-0 shadow-sm bg-white">
            <CardHeader className="border-b border-slate-100 bg-slate-50/50">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-slate-800">Daftar Pegawai</CardTitle>
                  <CardDescription className="text-slate-600">
                    Kelola dan monitor data pegawai
                  </CardDescription>
                </div>
              </div>
              
              {/* Filters */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 pt-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                  <Input
                    placeholder="Cari pegawai..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                  />
                </div>
                
                <Select value={unitKerja} onValueChange={setUnitKerja}>
                  <SelectTrigger className="border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                    <SelectValue placeholder="Unit Kerja" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Unit</SelectItem>
                    {unit_kerja_options.map((unit) => (
                      <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={statusKepegawaian} onValueChange={setStatusKepegawaian}>
                  <SelectTrigger className="border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Status</SelectItem>
                    {status_kepegawaian_options.map((status) => (
                      <SelectItem key={status} value={status}>{status}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={tmtFilter} onValueChange={setTmtFilter}>
                  <SelectTrigger className="border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                    <SelectValue placeholder="TMT Pensiun" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua TMT</SelectItem>
                    {Object.entries(tmt_filter_options).map(([key, label]) => (
                      <SelectItem key={key} value={key}>{label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button 
                  onClick={handleFilterChange}
                  className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
                >
                  Filter
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleResetFilter}
                  className="border-slate-200 text-slate-600 hover:bg-slate-50"
                >
                  Reset
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="border-b border-slate-100 bg-slate-50/30">
                      <TableHead className="font-semibold text-slate-700">NIP</TableHead>
                      <TableHead className="font-semibold text-slate-700">Nama</TableHead>
                      <TableHead className="font-semibold text-slate-700">Jabatan</TableHead>
                      <TableHead className="font-semibold text-slate-700">Unit Kerja</TableHead>
                      <TableHead className="font-semibold text-slate-700">Status</TableHead>
                      <TableHead className="font-semibold text-slate-700">TMT Pensiun</TableHead>
                      <TableHead className="text-right font-semibold text-slate-700">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pegawai_list.data.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-12 text-slate-500">
                          <div className="flex flex-col items-center gap-2">
                            <Users className="h-8 w-8 text-slate-300" />
                            <span>Tidak ada data pegawai</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      pegawai_list.data.map((pegawai) => (
                        <TableRow key={pegawai.nip} className="border-b border-slate-50 hover:bg-slate-50/50 transition-colors">
                          <TableCell className="font-mono text-slate-600 py-4">{pegawai.nip}</TableCell>
                          <TableCell className="font-medium text-slate-800 py-4">{pegawai.nama}</TableCell>
                          <TableCell className="text-slate-600 py-4">{pegawai.jabatan}</TableCell>
                          <TableCell className="text-slate-600 py-4">
                            <div className="flex items-center gap-1">
                              <Building className="h-4 w-4 text-slate-400" />
                              <span className="truncate max-w-[200px]">{pegawai.unit_kerja}</span>
                            </div>
                          </TableCell>
                          <TableCell className="py-4">
                            {getStatusBadge(pegawai.status_kepegawaian)}
                          </TableCell>
                          <TableCell className="text-slate-600 py-4">
                            {pegawai.tmt_pensiun ? formatDate(pegawai.tmt_pensiun) : '-'}
                          </TableCell>
                          <TableCell className="text-right py-4">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.location.href = `/kanwil/pegawai/${pegawai.nip}`}
                              className="gap-1 border-slate-200 text-slate-600 hover:bg-slate-50 hover:text-slate-800"
                            >
                              <Eye className="h-4 w-4" />
                              Detail
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
              
              {pegawai_list.data.length > 0 && (
                <div className="flex items-center justify-between px-6 py-4 border-t border-slate-100">
                  <div className="text-sm text-slate-500">
                    Menampilkan {pegawai_list.data.length} dari {pegawai_list.total} pegawai
                  </div>
                  <div className="flex items-center space-x-2">
                    {pegawai_list.current_page > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.href = `/kanwil/pegawai?page=${pegawai_list.current_page - 1}`}
                        className="border-slate-200 text-slate-600 hover:bg-slate-50"
                      >
                        Previous
                      </Button>
                    )}
                    <span className="text-sm text-slate-500">
                      Page {pegawai_list.current_page} of {pegawai_list.last_page}
                    </span>
                    {pegawai_list.current_page < pegawai_list.last_page && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.href = `/kanwil/pegawai?page=${pegawai_list.current_page + 1}`}
                        className="border-slate-200 text-slate-600 hover:bg-slate-50"
                      >
                        Next
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    </>
  )
}
