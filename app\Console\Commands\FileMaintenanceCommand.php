<?php

namespace App\Console\Commands;

use App\Services\FileUploadService;
use Illuminate\Console\Command;

class FileMaintenanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'file:maintenance {--days=30 : Number of days to keep files} {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old uploaded files and show storage statistics';

    /**
     * Execute the console command.
     */
    public function handle(FileUploadService $fileUploadService)
    {
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');

        $this->info("🧹 File Maintenance - Cleaning files older than {$days} days");
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No files will be deleted');
        }

        $this->newLine();

        // Show current storage stats
        $this->info('📊 Current Storage Statistics:');
        $stats = $fileUploadService->getStorageStats();
        
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Files', $stats['total_files']],
                ['Total Size', $stats['total_size_formatted']],
                ['Average File Size', $fileUploadService->formatFileSize($stats['average_file_size'])],
            ]
        );

        if (!empty($stats['type_distribution'])) {
            $this->newLine();
            $this->info('📁 File Type Distribution:');
            $typeData = [];
            foreach ($stats['type_distribution'] as $type => $count) {
                $typeData[] = [strtoupper($type), $count];
            }
            $this->table(['File Type', 'Count'], $typeData);
        }

        $this->newLine();

        // Cleanup old files
        if (!$dryRun) {
            $deletedCount = $fileUploadService->cleanupOldFiles($days);
            
            if ($deletedCount > 0) {
                $this->info("✅ Cleaned up {$deletedCount} old files");
            } else {
                $this->info("✅ No old files found to clean up");
            }
        } else {
            $this->info("🔍 DRY RUN: Would clean up files older than {$days} days");
        }

        // Show updated stats if not dry run
        if (!$dryRun) {
            $this->newLine();
            $newStats = $fileUploadService->getStorageStats();
            
            $this->info('📊 Updated Storage Statistics:');
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Files', $newStats['total_files']],
                    ['Total Size', $newStats['total_size_formatted']],
                    ['Space Saved', $fileUploadService->formatFileSize($stats['total_size'] - $newStats['total_size'])],
                ]
            );
        }

        return Command::SUCCESS;
    }
}