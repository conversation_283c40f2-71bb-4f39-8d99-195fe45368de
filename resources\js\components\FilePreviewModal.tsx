import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  X, 
  Upload, 
  FileText, 
  Image as ImageIcon, 
  Download,
  AlertCircle,
  Check
} from 'lucide-react'

interface FilePreviewModalProps {
  isOpen: boolean
  onClose: () => void
  file: File | null
  jenis_dokumen: string
  onFileChange: (file: File | null) => void
  maxSizeKB?: number
}

export default function FilePreviewModal({
  isOpen,
  onClose,
  file,
  jenis_dokumen,
  onFileChange,
  maxSizeKB = 350
}: FilePreviewModalProps) {
  const [dragActive, setDragActive] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Generate preview URL when file changes
  React.useEffect(() => {
    if (file) {
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
      return () => URL.revokeObjectURL(url)
    } else {
      setPreviewUrl(null)
    }
  }, [file])

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const validateFile = (file: File): string | null => {
    // Check file size
    const maxBytes = maxSizeKB * 1024
    if (file.size > maxBytes) {
      return `Ukuran file terlalu besar. Maksimal ${maxSizeKB} KB`
    }

    // Check file type
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      return 'Format file tidak didukung. Gunakan PDF, DOC, DOCX, JPG, atau PNG'
    }

    return null
  }

  const handleFileSelect = (selectedFile: File) => {
    const validationError = validateFile(selectedFile)
    if (validationError) {
      setError(validationError)
      return
    }

    setError(null)
    onFileChange(selectedFile)
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      handleFileSelect(selectedFile)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const droppedFile = e.dataTransfer.files?.[0]
    if (droppedFile) {
      handleFileSelect(droppedFile)
    }
  }

  const handleRemoveFile = () => {
    setError(null)
    onFileChange(null)
  }

  const isImage = file?.type.startsWith('image/')
  const isPDF = file?.type === 'application/pdf'

  const renderPreview = () => {
    if (!file || !previewUrl) return null

    if (isImage) {
      return (
        <div className="relative">
          <img 
            src={previewUrl} 
            alt="Preview" 
            className="max-w-full max-h-96 mx-auto rounded-lg border"
          />
        </div>
      )
    } else if (isPDF) {
      return (
        <div className="text-center py-8">
          <FileText className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <p className="text-sm text-gray-600">
            PDF Preview tidak tersedia. 
            <Button 
              variant="link" 
              className="p-0 h-auto text-blue-600"
              onClick={() => window.open(previewUrl || '', '_blank')}
            >
              Buka di tab baru
            </Button>
          </p>
        </div>
      )
    } else {
      return (
        <div className="text-center py-8">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <p className="text-sm text-gray-600">Preview tidak tersedia untuk file ini</p>
        </div>
      )
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Preview File: {jenis_dokumen}</DialogTitle>
          <DialogDescription>
            Preview dan kelola file dokumen sebelum menyimpan
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current File Info */}
          {file && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Check className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-green-800">{file.name}</p>
                    <p className="text-sm text-green-600">
                      {formatFileSize(file.size)} • {file.type}
                    </p>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleRemoveFile}
                  className="gap-2"
                >
                  <X className="h-4 w-4" />
                  Hapus
                </Button>
              </div>
            </div>
          )}

          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* File Upload Area */}
          <div className="space-y-4">
            <Label htmlFor="file-upload">
              {file ? 'Ganti File' : 'Upload File'}
            </Label>
            
            <div
              className={`
                border-2 border-dashed rounded-lg p-8 text-center transition-colors
                ${dragActive 
                  ? 'border-blue-400 bg-blue-50' 
                  : 'border-gray-300 hover:border-gray-400'
                }
              `}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-900">
                  {dragActive ? 'Lepas file di sini' : 'Drag & drop file atau klik untuk upload'}
                </p>
                <p className="text-sm text-gray-500">
                  PDF, DOC, DOCX, JPG, PNG (Maks. {maxSizeKB} KB)
                </p>
              </div>
              
              <Input
                id="file-upload"
                type="file"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                onChange={handleFileInput}
                className="hidden"
              />
              
              <Button
                type="button"
                variant="outline"
                className="mt-4"
                onClick={() => document.getElementById('file-upload')?.click()}
              >
                Pilih File
              </Button>
            </div>
          </div>

          {/* File Preview */}
          {file && (
            <div className="border rounded-lg">
              <div className="p-4 border-b bg-gray-50">
                <h3 className="font-medium">Preview File</h3>
                <p className="text-sm text-gray-600">{file.name}</p>
              </div>
              <div className="p-4">
                {renderPreview()}
              </div>
            </div>
          )}

          {/* File Requirements */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">Persyaratan File:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Format: PDF, DOC, DOCX, JPG, PNG</li>
              <li>• Ukuran maksimal: {maxSizeKB} KB</li>
              <li>• File harus jelas dan dapat dibaca</li>
              <li>• Pastikan semua informasi terlihat lengkap</li>
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Batal
          </Button>
          <Button 
            onClick={onClose}
            disabled={!file}
            className="gap-2"
          >
            <Check className="h-4 w-4" />
            Gunakan File
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
