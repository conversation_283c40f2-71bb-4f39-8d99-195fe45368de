import { User } from '@/types'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { LogOut, User2 } from 'lucide-react'

interface Props {
  user: User
}

export default function Navbar({ user }: Props) {
  const handleLogout = () => {
    window.location.href = '/logout'
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'Kanwil':
        return 'Kantor Wilayah'
      case 'Kabupaten':
        return 'Operator Kabupaten/Kota'
      case 'Admin Pusat':
        return 'Admin Pusat'
      default:
        return role
    }
  }

  return (
    <nav className="bg-white shadow-sm border-b border-slate-200 fixed top-0 left-0 right-0 z-40">
      <div className="px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex items-center justify-between">
          {/* Logo & Title */}
          <div className="flex items-center space-x-4 min-w-0 flex-1">
            <div className="min-w-0">
              <h1 className="text-lg sm:text-xl font-bold text-slate-800 truncate">
                Sistem Pengajuan Pensiun ASN
              </h1>
              <p className="text-xs sm:text-sm text-slate-500 truncate">
                Kementerian Agama NTB
              </p>
            </div>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <div className="hidden lg:block text-right">
              <p className="text-sm font-medium text-slate-800 truncate max-w-[200px]">{user.name}</p>
              <p className="text-xs text-slate-500 truncate max-w-[200px]">{getRoleLabel(user.role)}</p>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-10 w-10 rounded-full hover:bg-slate-50">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-blue-50 text-blue-700 font-semibold">
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-64 z-[100] shadow-lg border border-slate-200 bg-white"
                align="end"
                forceMount
                sideOffset={8}
              >
                <DropdownMenuLabel className="font-normal p-4 bg-slate-50/50">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-semibold leading-none text-slate-800">{user.name}</p>
                    <p className="text-xs leading-none text-slate-500 mt-1">
                      {user.email}
                    </p>
                    <p className="text-xs leading-none text-blue-600 font-medium mt-1">
                      {getRoleLabel(user.role)}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="border-slate-100" />
                <div className="p-1">
                  <DropdownMenuItem className="cursor-pointer hover:bg-slate-50 rounded-lg p-3 transition-colors">
                    <User2 className="mr-3 h-4 w-4 text-slate-500" />
                    <span className="text-slate-700">Profil</span>
                  </DropdownMenuItem>
                </div>
                <DropdownMenuSeparator className="border-slate-100" />
                <div className="p-1">
                  <DropdownMenuItem
                    className="cursor-pointer text-red-600 focus:text-red-600 hover:bg-red-50 rounded-lg p-3 transition-colors"
                    onClick={handleLogout}
                  >
                    <LogOut className="mr-3 h-4 w-4" />
                    <span>Keluar</span>
                  </DropdownMenuItem>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </nav>
  )
}
