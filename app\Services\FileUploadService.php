<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadService
{
    // Max file size in bytes (350KB)
    const MAX_FILE_SIZE = 358400;
    
    // Allowed MIME types
    const ALLOWED_MIME_TYPES = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg', 
        'image/png'
    ];

    // Allowed file extensions
    const ALLOWED_EXTENSIONS = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];

    /**
     * Validate uploaded file
     */
    public function validateFile(UploadedFile $file): array
    {
        $errors = [];

        // Check file size
        if ($file->getSize() > self::MAX_FILE_SIZE) {
            $errors[] = 'Ukuran file maksimal 350KB';
        }

        // Check MIME type
        if (!in_array($file->getMimeType(), self::ALLOWED_MIME_TYPES)) {
            $errors[] = 'Tipe file tidak diizinkan. Gunakan PDF, DOC, DOCX, JPG, atau PNG';
        }

        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, self::ALLOWED_EXTENSIONS)) {
            $errors[] = 'Ekstensi file tidak diizinkan';
        }

        // Check if file is corrupted
        if ($file->getError() !== UPLOAD_ERR_OK) {
            $errors[] = 'File rusak atau tidak dapat diupload';
        }

        return $errors;
    }

    /**
     * Upload file for pengajuan
     */
    public function uploadDokumenPengajuan(UploadedFile $file, int $pengajuanId, string $jenisDokumen): array
    {
        // Validate file first
        $errors = $this->validateFile($file);
        if (!empty($errors)) {
            throw new \InvalidArgumentException(implode(', ', $errors));
        }

        // Generate unique filename
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $extension = $file->getClientOriginalExtension();
        $fileName = Str::slug($originalName) . '_' . time() . '.' . $extension;

        // Store file
        $path = $file->storeAs(
            "pengajuan/{$pengajuanId}/dokumen",
            $fileName,
            'public'
        );

        if (!$path) {
            throw new \Exception('Gagal mengupload file');
        }

        return [
            'original_name' => $file->getClientOriginalName(),
            'file_name' => $fileName,
            'path' => $path,
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'url' => Storage::url($path)
        ];
    }

    /**
     * Delete file from storage
     */
    public function deleteFile(string $path): bool
    {
        if (Storage::disk('public')->exists($path)) {
            return Storage::disk('public')->delete($path);
        }
        
        return true; // File tidak ada, anggap berhasil
    }

    /**
     * Get file info
     */
    public function getFileInfo(string $path): ?array
    {
        if (!Storage::disk('public')->exists($path)) {
            return null;
        }

        $fullPath = Storage::disk('public')->path($path);
        
        return [
            'path' => $path,
            'url' => Storage::url($path),
            'size' => Storage::disk('public')->size($path),
            'last_modified' => Storage::disk('public')->lastModified($path),
            'exists' => true
        ];
    }

    /**
     * Format file size to human readable
     */
    public function formatFileSize(int $bytes): string
    {
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Check if file type is image
     */
    public function isImage(string $mimeType): bool
    {
        return in_array($mimeType, [
            'image/jpeg',
            'image/jpg',
            'image/png'
        ]);
    }

    /**
     * Check if file type is document
     */
    public function isDocument(string $mimeType): bool
    {
        return in_array($mimeType, [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ]);
    }

    /**
     * Get file icon based on mime type
     */
    public function getFileIcon(string $mimeType): string
    {
        return match($mimeType) {
            'application/pdf' => 'file-pdf',
            'application/msword', 
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'file-word',
            'image/jpeg', 'image/jpg', 'image/png' => 'file-image',
            default => 'file'
        };
    }

    /**
     * Generate thumbnail for images (optional feature)
     */
    public function generateThumbnail(string $imagePath, int $width = 150, int $height = 150): ?string
    {
        // Implementasi thumbnail generation bisa ditambahkan nanti
        // Untuk sekarang return null
        return null;
    }

    /**
     * Clean up old files (for maintenance)
     */
    public function cleanupOldFiles(int $daysOld = 30): int
    {
        $deletedCount = 0;
        $cutoffTime = now()->subDays($daysOld)->timestamp;
        
        $files = Storage::disk('public')->allFiles('pengajuan');
        
        foreach ($files as $file) {
            $lastModified = Storage::disk('public')->lastModified($file);
            
            if ($lastModified < $cutoffTime) {
                Storage::disk('public')->delete($file);
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }

    /**
     * Get storage usage statistics
     */
    public function getStorageStats(): array
    {
        $files = Storage::disk('public')->allFiles('pengajuan');
        $totalSize = 0;
        $fileCount = 0;
        $typeStats = [];

        foreach ($files as $file) {
            $size = Storage::disk('public')->size($file);
            $totalSize += $size;
            $fileCount++;

            $extension = pathinfo($file, PATHINFO_EXTENSION);
            $typeStats[$extension] = ($typeStats[$extension] ?? 0) + 1;
        }

        return [
            'total_files' => $fileCount,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatFileSize($totalSize),
            'type_distribution' => $typeStats,
            'average_file_size' => $fileCount > 0 ? $totalSize / $fileCount : 0
        ];
    }
}
