import { Head, useForm } from '@inertiajs/react'
import { FormEventHandler } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Eye, EyeOff, LogIn } from 'lucide-react'
import { useState } from 'react'

interface LoginForm {
  email: string
  password: string
  remember: boolean
}

export default function Login({ status, canResetPassword = false }: { status?: string; canResetPassword?: boolean }) {
  const [showPassword, setShowPassword] = useState(false)
  const { data, setData, post, processing, errors, reset } = useForm({
    email: '',
    password: '',
    remember: false,
  })

  const submit: FormEventHandler = (e) => {
    e.preventDefault()

    post('/login', {
      onFinish: () => reset('password'),
    })
  }

  return (
    <>
      <Head title="Masuk" />

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl font-bold text-gray-900 flex items-center justify-center gap-2">
                <LogIn className="h-6 w-6" />
                Masuk ke Sistem
              </CardTitle>
              <CardDescription className="text-gray-600">
                Sistem Pengajuan Pensiun ASN
              </CardDescription>
            </CardHeader>
            <CardContent>
              {status && (
                <Alert className="mb-4">
                  <AlertDescription>{status}</AlertDescription>
                </Alert>
              )}

              <form onSubmit={submit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    name="email"
                    value={data.email}
                    className={errors.email ? 'border-red-500' : ''}
                    autoComplete="username"
                    autoFocus
                    onChange={(e) => setData('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="text-sm text-red-600">{errors.email}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      name="password"
                      value={data.password}
                      className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
                      autoComplete="current-password"
                      onChange={(e) => setData('password', e.target.value)}
                      placeholder="Masukkan password"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>
                  </div>
                  {errors.password && (
                    <p className="text-sm text-red-600">{errors.password}</p>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    id="remember"
                    type="checkbox"
                    name="remember"
                    checked={data.remember}
                    onChange={(e) => setData('remember', e.target.checked)}
                    className="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                  />
                  <Label htmlFor="remember" className="text-sm">
                    Ingat saya
                  </Label>
                </div>

                <Button type="submit" className="w-full" disabled={processing}>
                  {processing ? 'Memproses...' : 'Masuk'}
                </Button>

                {canResetPassword && (
                  <div className="text-center">
                    <a
                      href="/forgot-password"
                      className="text-sm text-indigo-600 hover:text-indigo-500"
                    >
                      Lupa password?
                    </a>
                  </div>
                )}
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  )
}
