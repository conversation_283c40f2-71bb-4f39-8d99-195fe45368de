{"compilerOptions": {"allowJs": true, "module": "ESNext", "moduleResolution": "bundler", "jsx": "react-jsx", "jsxImportSource": "react", "strict": true, "isolatedModules": true, "target": "ESNext", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "types": ["vite/client", "node"], "paths": {"@/*": ["./resources/js/*"], "ziggy-js": ["./vendor/tightenco/ziggy", "./node_modules/ziggy-js"]}}, "include": ["resources/js/**/*.ts", "resources/js/**/*.tsx", "resources/js/**/*.d.ts", "resources/js/types/vite-env.d.ts"]}