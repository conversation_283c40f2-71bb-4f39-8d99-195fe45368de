<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Kanwil NTB (Superuser)
        User::create([
            'name' => 'Kanwil Kemenag NTB',
            'email' => '<EMAIL>',
            'password' => Hash::make('kanwilntb123'),
            'role' => 'Kanwil',
            'email_verified_at' => now()
        ]);

        // Kabupaten/Kota di NTB
        $kabupatenKota = [
            ['name' => 'Kemenag Kota Mataram', 'email' => '<EMAIL>'],
            ['name' => 'Kemenag Kabupaten Lombok Barat', 'email' => '<EMAIL>'],
            ['name' => 'Kemenag Kabupaten Lombok Tengah', 'email' => '<EMAIL>'],
            ['name' => 'Kemenag Kabupaten Lombok Timur', 'email' => '<EMAIL>'],
            ['name' => 'Kemenag Kabupaten Lombok Utara', 'email' => '<EMAIL>'],
            ['name' => 'Kemenag Kota Bima', 'email' => '<EMAIL>'],
            ['name' => 'Kemenag Kabupaten Bima', 'email' => '<EMAIL>'],
            ['name' => 'Kemenag Kabupaten Dompu', 'email' => '<EMAIL>'],
            ['name' => 'Kemenag Kabupaten Sumbawa', 'email' => '<EMAIL>'],
            ['name' => 'Kemenag Kabupaten Sumbawa Barat', 'email' => '<EMAIL>'],
        ];

        foreach ($kabupatenKota as $kabkota) {
            User::create([
                'name' => $kabkota['name'],
                'email' => $kabkota['email'],
                'password' => Hash::make('password123'),
                'role' => 'Kabupaten',
                'email_verified_at' => now()
            ]);
        }

        // Admin Pusat
        User::create([
            'name' => 'Admin Pusat Kemenag',
            'email' => '<EMAIL>',
            'password' => Hash::make('adminpusat123'),
            'role' => 'Admin Pusat',
            'email_verified_at' => now()
        ]);

        // Demo users untuk testing
        User::create([
            'name' => 'Demo Kanwil',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'Kanwil',
            'email_verified_at' => now()
        ]);

        User::create([
            'name' => 'Demo Kabupaten',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'Kabupaten',
            'email_verified_at' => now()
        ]);

        User::create([
            'name' => 'Demo Admin Pusat',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'Admin Pusat',
            'email_verified_at' => now()
        ]);
    }
}