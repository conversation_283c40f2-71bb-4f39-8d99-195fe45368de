<?php

namespace App\Http\Controllers;

use App\Models\PegawaiCache;
use App\Models\PengajuanPensiun;
use App\Models\Dokumen;
use App\Models\LogAktivitas;
use App\Services\PegawaiService;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class PengajuanController extends Controller
{
    public function __construct(
        private PegawaiService $pegawaiService,
        private FileUploadService $fileUploadService
    ) {}

    /**
     * Display a listing of pengajuan
     */
    public function index(Request $request): Response
    {
        $query = PengajuanPensiun::with(['pegawai', 'user', 'penyetuju'])
            ->where('user_id', auth()->id())
            ->latest();

        // Filter berdasarkan status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter berdasarkan jenis pensiun
        if ($request->jenis_pensiun) {
            $query->where('jenis_pensiun', $request->jenis_pensiun);
        }

        // Search
        if ($request->search) {
            $query->whereHas('pegawai', function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->search . '%')
                  ->orWhere('nip', 'like', '%' . $request->search . '%');
            });
        }

        $pengajuanList = $query->paginate(20);

        return Inertia::render('Kabupaten/Pengajuan/Index', [
            'pengajuan_list' => $pengajuanList,
            'filters' => $request->only(['status', 'jenis_pensiun', 'search']),
            'status_options' => ['Draft', 'Diajukan', 'Disetujui', 'Ditolak'],
            'jenis_pensiun_options' => ['BUP', 'Sakit', 'Janda/Duda', 'APS']
        ]);
    }

    /**
     * Show the form for creating a new pengajuan (select pegawai)
     */
    public function createSelect(Request $request): Response
    {
        $query = PegawaiCache::query();

        // Search functionality
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->search . '%')
                  ->orWhere('nip', 'like', '%' . $request->search . '%');
            });
        }

        $pegawaiList = $query->orderBy('nama')->paginate(20);

        return Inertia::render('Kabupaten/Pengajuan/CreateSelect', [
            'pegawai_list' => $pegawaiList,
            'search' => $request->search
        ]);
    }

    /**
     * Display the specified pengajuan
     */
    public function show(int $id): Response
    {
        $pengajuan = PengajuanPensiun::with(['pegawai', 'dokumen', 'user', 'penyetuju'])
            ->where('user_id', auth()->id())
            ->findOrFail($id);

        return Inertia::render('Kabupaten/Pengajuan/Show', [
            'pengajuan' => $pengajuan,
            'dokumen_wajib' => $pengajuan->getDokumenWajib(),
            'can_edit' => in_array($pengajuan->status, ['Draft', 'Ditolak']),
            'can_submit' => $pengajuan->status === 'Draft',
            'log_aktivitas' => $pengajuan->logAktivitas()->with('user')->latest()->get()
        ]);
    }

    /**
     * Tampilkan form pembuatan pengajuan pensiun
     */
    public function create(string $nip): Response
    {
        $pegawai = PegawaiCache::where('nip', $nip)->first();
        
        if (!$pegawai) {
            // Coba ambil dari API jika tidak ada di cache
            $pegawaiData = $this->pegawaiService->getPegawaiByNip($nip);
            if (!$pegawaiData) {
                abort(404, 'Pegawai tidak ditemukan');
            }
            $pegawai = PegawaiCache::where('nip', $nip)->first();
        }

        // Check apakah sudah ada pengajuan aktif
        if ($pegawai->hasPengajuanAktif()) {
            return redirect()->route('kabupaten.dashboard')
                ->with('error', 'Pegawai ini sudah memiliki pengajuan aktif');
        }

        return Inertia::render('Dashboard/PengajuanForm', [
            'pegawai' => $pegawai,
            'jenis_pensiun_options' => ['BUP', 'Sakit', 'Janda/Duda', 'APS']
        ]);
    }

    /**
     * Simpan pengajuan pensiun baru
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'nip' => 'required|string',
            'jenis_pensiun' => 'required|in:BUP,Sakit,Janda/Duda,APS',
            'dokumen' => 'required|array',
            'dokumen.*' => 'required|file|max:350|mimes:pdf,doc,docx,jpg,jpeg,png'
        ]);

        $pegawai = PegawaiCache::where('nip', $request->nip)->first();
        if (!$pegawai) {
            return back()->withErrors(['nip' => 'Pegawai tidak ditemukan']);
        }

        // Check pengajuan aktif lagi
        if ($pegawai->hasPengajuanAktif()) {
            return back()->withErrors(['nip' => 'Pegawai sudah memiliki pengajuan aktif']);
        }

        try {
            // Buat pengajuan
            $pengajuan = PengajuanPensiun::create([
                'nip' => $request->nip,
                'user_id' => auth()->id(),
                'jenis_pensiun' => $request->jenis_pensiun,
                'status' => 'Draft'
            ]);

            // Upload dan simpan dokumen
            $dokumenWajib = $pengajuan->getDokumenWajib();
            $uploadedDokumen = [];

            foreach ($request->file('dokumen') as $jenisDokumen => $file) {
                if (!in_array($jenisDokumen, $dokumenWajib)) {
                    continue;
                }

                try {
                    $fileInfo = $this->fileUploadService->uploadDokumenPengajuan(
                        $file, 
                        $pengajuan->id, 
                        $jenisDokumen
                    );

                    Dokumen::create([
                        'pengajuan_id' => $pengajuan->id,
                        'jenis_dokumen' => $jenisDokumen,
                        'nama_file' => $fileInfo['original_name'],
                        'path_file' => $fileInfo['path'],
                        'mime_type' => $fileInfo['mime_type'],
                        'ukuran_file' => $fileInfo['size'],
                        'wajib' => in_array($jenisDokumen, $dokumenWajib)
                    ]);

                    $uploadedDokumen[] = $jenisDokumen;
                } catch (\Exception $e) {
                    throw new \Exception("Gagal upload dokumen {$jenisDokumen}: " . $e->getMessage());
                }
            }

            // Log aktivitas
            LogAktivitas::logActivity(
                'membuat',
                'pengajuan_pensiun',
                $pengajuan->id,
                "Membuat pengajuan pensiun {$request->jenis_pensiun} untuk pegawai {$pegawai->nama}"
            );

            return redirect()->route('kabupaten.dashboard')
                ->with('success', 'Pengajuan pensiun berhasil dibuat');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Gagal membuat pengajuan: ' . $e->getMessage()]);
        }
    }

    /**
     * Tampilkan form edit pengajuan
     */
    public function edit(int $id): Response
    {
        $pengajuan = PengajuanPensiun::with(['pegawai', 'dokumen', 'user'])
            ->where('user_id', auth()->id())
            ->findOrFail($id);

        // Hanya bisa edit jika status Draft atau Ditolak
        if (!in_array($pengajuan->status, ['Draft', 'Ditolak'])) {
            abort(403, 'Pengajuan tidak dapat diedit');
        }

        $dokumenWajib = $pengajuan->getDokumenWajib();
        $dokumenTeruplod = $pengajuan->dokumen->pluck('jenis_dokumen')->toArray();
        $dokumenKurang = array_diff($dokumenWajib, $dokumenTeruplod);

        return Inertia::render('Kabupaten/PengajuanEdit', [
            'pengajuan' => $pengajuan,
            'dokumen_wajib' => $dokumenWajib,
            'dokumen_kurang' => $dokumenKurang,
            'jenis_pensiun_options' => ['BUP', 'Sakit', 'Janda/Duda', 'APS']
        ]);
    }

    /**
     * Update pengajuan pensiun
     */
    public function update(Request $request, int $id): RedirectResponse
    {
        $pengajuan = PengajuanPensiun::where('user_id', auth()->id())->findOrFail($id);

        if (!in_array($pengajuan->status, ['Draft', 'Ditolak'])) {
            return back()->withErrors(['error' => 'Pengajuan tidak dapat diedit']);
        }

        $request->validate([
            'jenis_pensiun' => 'required|in:BUP,Sakit,Janda/Duda,APS',
            'dokumen' => 'sometimes|array',
            'dokumen.*' => 'required|file|max:350|mimes:pdf,doc,docx,jpg,jpeg,png'
        ]);

        try {
            $pengajuan->update([
                'jenis_pensiun' => $request->jenis_pensiun,
                'status' => 'Draft'
            ]);

            // Upload dokumen baru jika ada
            if ($request->hasFile('dokumen')) {
                foreach ($request->file('dokumen') as $jenisDokumen => $file) {
                    try {
                        // Hapus dokumen lama jika ada
                        $dokumenLama = $pengajuan->dokumen()->where('jenis_dokumen', $jenisDokumen)->first();
                        if ($dokumenLama) {
                            $this->fileUploadService->deleteFile($dokumenLama->path_file);
                            $dokumenLama->delete();
                        }

                        // Upload dokumen baru
                        $fileInfo = $this->fileUploadService->uploadDokumenPengajuan(
                            $file,
                            $pengajuan->id,
                            $jenisDokumen
                        );

                        Dokumen::create([
                            'pengajuan_id' => $pengajuan->id,
                            'jenis_dokumen' => $jenisDokumen,
                            'nama_file' => $fileInfo['original_name'],
                            'path_file' => $fileInfo['path'],
                            'mime_type' => $fileInfo['mime_type'],
                            'ukuran_file' => $fileInfo['size'],
                            'wajib' => true
                        ]);
                    } catch (\Exception $e) {
                        throw new \Exception("Gagal upload dokumen {$jenisDokumen}: " . $e->getMessage());
                    }
                }
            }

            // Log aktivitas
            LogAktivitas::logActivity(
                'mengupdate',
                'pengajuan_pensiun',
                $pengajuan->id,
                "Mengupdate pengajuan pensiun {$request->jenis_pensiun}"
            );

            return redirect()->route('kabupaten.dashboard')
                ->with('success', 'Pengajuan pensiun berhasil diupdate');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Gagal mengupdate pengajuan: ' . $e->getMessage()]);
        }
    }

    /**
     * Submit pengajuan (ubah status dari Draft ke Diajukan)
     */
    public function submit(int $id): RedirectResponse
    {
        $pengajuan = PengajuanPensiun::where('user_id', auth()->id())->findOrFail($id);

        if ($pengajuan->status !== 'Draft') {
            return back()->withErrors(['error' => 'Pengajuan tidak dapat disubmit']);
        }

        // Validasi kelengkapan dokumen
        $dokumenWajib = $pengajuan->getDokumenWajib();
        $dokumenTeruplod = $pengajuan->dokumen->pluck('jenis_dokumen')->toArray();
        $dokumenKurang = array_diff($dokumenWajib, $dokumenTeruplod);

        if (!empty($dokumenKurang)) {
            return back()->withErrors([
                'error' => 'Dokumen belum lengkap: ' . implode(', ', $dokumenKurang)
            ]);
        }

        $pengajuan->update(['status' => 'Diajukan']);

        // Log aktivitas
        LogAktivitas::logActivity(
            'submit',
            'pengajuan_pensiun',
            $pengajuan->id,
            "Submit pengajuan pensiun untuk review"
        );

        return redirect()->route('kabupaten.dashboard')
            ->with('success', 'Pengajuan pensiun berhasil disubmit untuk review');
    }

    /**
     * Display a listing of pengajuan for Kanwil (superadmin) and Kabupaten (operator)
     */
    public function indexKanwil(Request $request): Response
    {
        $query = PengajuanPensiun::with(['pegawai', 'user', 'penyetuju'])
            ->latest();

        // Jika bukan Kanwil (superadmin), hanya tampilkan pengajuan user sendiri
        if (auth()->user()->role !== 'Kanwil') {
            $query->where('user_id', auth()->id());
        }

        // Filter berdasarkan status
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Filter berdasarkan jenis pensiun
        if ($request->jenis_pensiun) {
            $query->where('jenis_pensiun', $request->jenis_pensiun);
        }

        // Search
        if ($request->search) {
            $query->whereHas('pegawai', function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->search . '%')
                  ->orWhere('nip', 'like', '%' . $request->search . '%');
            });
        }

        $pengajuanList = $query->paginate(20);

        return Inertia::render('Kanwil/Pengajuan/Index', [
            'pengajuan_list' => $pengajuanList,
            'filters' => $request->only(['status', 'jenis_pensiun', 'search']),
            'status_options' => ['Draft', 'Diajukan', 'Disetujui', 'Ditolak'],
            'jenis_pensiun_options' => ['BUP', 'Sakit', 'Janda/Duda', 'APS'],
            'is_superadmin' => auth()->user()->role === 'Kanwil'
        ]);
    }

    /**
     * Show the form for creating a new pengajuan for Kanwil (select pegawai)
     */
    public function createSelectKanwil(Request $request): Response
    {
        $query = PegawaiCache::query();

        // Search functionality
        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('nama', 'like', '%' . $request->search . '%')
                  ->orWhere('nip', 'like', '%' . $request->search . '%');
            });
        }

        $pegawaiList = $query->orderBy('nama')->paginate(20);

        return Inertia::render('Kanwil/Pengajuan/CreateSelect', [
            'pegawai_list' => $pegawaiList,
            'search' => $request->search
        ]);
    }

    /**
     * Tampilkan form pembuatan pengajuan pensiun untuk Kanwil
     */
    public function createKanwil(string $nip): Response
    {
        $pegawai = PegawaiCache::where('nip', $nip)->first();

        if (!$pegawai) {
            // Coba ambil dari API jika tidak ada di cache
            try {
                $pegawaiData = $this->pegawaiService->getPegawaiByNip($nip);
                $pegawai = PegawaiCache::updateOrCreate(
                    ['nip' => $nip],
                    $pegawaiData
                );
            } catch (\Exception $e) {
                return redirect()->route('kanwil.pengajuan.create')
                    ->withErrors(['error' => 'Pegawai dengan NIP ' . $nip . ' tidak ditemukan']);
            }
        }

        return Inertia::render('Kanwil/Pengajuan/Create', [
            'pegawai' => $pegawai,
            'dokumen_wajib' => $this->getDokumenWajib($pegawai->jenis_kelamin ?? 'L')
        ]);
    }

    /**
     * Display the specified pengajuan for Kanwil and Kabupaten
     */
    public function showKanwil(int $id): Response
    {
        $query = PengajuanPensiun::with(['pegawai', 'dokumen', 'user', 'penyetuju']);

        // Jika bukan Kanwil (superadmin), hanya bisa lihat pengajuan sendiri
        if (auth()->user()->role !== 'Kanwil') {
            $query->where('user_id', auth()->id());
        }

        $pengajuan = $query->findOrFail($id);

        $isKanwil = auth()->user()->role === 'Kanwil';
        $isOwner = $pengajuan->user_id === auth()->id();

        return Inertia::render('Kanwil/Pengajuan/Show', [
            'pengajuan' => $pengajuan,
            'dokumen_wajib' => $pengajuan->getDokumenWajib(),
            'can_edit' => $isKanwil || ($isOwner && in_array($pengajuan->status, ['Draft', 'Ditolak'])),
            'can_delete' => $isKanwil, // Hanya Kanwil yang bisa delete
            'is_superadmin' => $isKanwil,
            'log_aktivitas' => $pengajuan->logAktivitas()->with('user')->latest()->get()
        ]);
    }

    /**
     * Show the form for editing the specified pengajuan for Kanwil and Kabupaten
     */
    public function editKanwil(int $id): Response
    {
        $query = PengajuanPensiun::with(['pegawai', 'dokumen', 'user']);

        // Jika bukan Kanwil (superadmin), hanya bisa edit pengajuan sendiri
        if (auth()->user()->role !== 'Kanwil') {
            $query->where('user_id', auth()->id());
        }

        $pengajuan = $query->findOrFail($id);

        // Cek apakah bisa diedit
        $isKanwil = auth()->user()->role === 'Kanwil';
        $isOwner = $pengajuan->user_id === auth()->id();
        $canEdit = $isKanwil || ($isOwner && in_array($pengajuan->status, ['Draft', 'Ditolak']));

        if (!$canEdit) {
            abort(403, 'Anda tidak dapat mengedit pengajuan ini.');
        }

        return Inertia::render('Kanwil/Pengajuan/Edit', [
            'pengajuan' => $pengajuan,
            'dokumen_wajib' => $pengajuan->getDokumenWajib(),
            'is_superadmin' => $isKanwil
        ]);
    }

    /**
     * Store pengajuan pensiun baru untuk Kanwil
     */
    public function storeKanwil(Request $request): RedirectResponse
    {
        $request->validate([
            'nip' => 'required|string',
            'jenis_pensiun' => 'required|in:BUP,Sakit,Janda/Duda,APS',
            'dokumen' => 'required|array',
            'dokumen.*' => 'file|max:10240', // 10MB max per file
        ]);

        $pegawai = PegawaiCache::where('nip', $request->nip)->firstOrFail();

        // Create pengajuan
        $pengajuan = PengajuanPensiun::create([
            'nip' => $request->nip,
            'jenis_pensiun' => $request->jenis_pensiun,
            'status' => 'Draft',
            'user_id' => auth()->id(),
        ]);

        // Upload dokumen
        if ($request->hasFile('dokumen')) {
            foreach ($request->file('dokumen') as $jenisDokumen => $file) {
                $uploadResult = $this->fileUploadService->uploadFile($file, 'pengajuan');

                Dokumen::create([
                    'pengajuan_id' => $pengajuan->id,
                    'jenis_dokumen' => $jenisDokumen,
                    'nama_file' => $uploadResult['original_name'],
                    'path_file' => $uploadResult['path'],
                    'ukuran_file' => $uploadResult['size'],
                    'mime_type' => $uploadResult['mime_type'],
                ]);
            }
        }

        // Log aktivitas
        LogAktivitas::create([
            'pengajuan_id' => $pengajuan->id,
            'user_id' => auth()->id(),
            'aktivitas' => 'Pengajuan dibuat',
            'keterangan' => 'Pengajuan pensiun ' . $request->jenis_pensiun . ' dibuat oleh Kanwil'
        ]);

        return redirect()->route('kanwil.pengajuan-kelola.show', $pengajuan->id)
            ->with('success', 'Pengajuan berhasil dibuat');
    }

    /**
     * Update pengajuan pensiun untuk Kanwil dan Kabupaten
     */
    public function updateKanwil(Request $request, int $id): RedirectResponse
    {
        $query = PengajuanPensiun::query();

        // Jika bukan Kanwil (superadmin), hanya bisa update pengajuan sendiri
        if (auth()->user()->role !== 'Kanwil') {
            $query->where('user_id', auth()->id());
        }

        $pengajuan = $query->findOrFail($id);

        // Cek apakah bisa diedit
        $isKanwil = auth()->user()->role === 'Kanwil';
        $isOwner = $pengajuan->user_id === auth()->id();
        $canEdit = $isKanwil || ($isOwner && in_array($pengajuan->status, ['Draft', 'Ditolak']));

        if (!$canEdit) {
            return back()->withErrors(['error' => 'Anda tidak dapat mengedit pengajuan ini.']);
        }

        $request->validate([
            'jenis_pensiun' => 'required|in:BUP,Sakit,Janda/Duda,APS',
            'dokumen' => 'sometimes|array',
            'dokumen.*' => 'file|max:10240',
        ]);

        // Update pengajuan
        $pengajuan->update([
            'jenis_pensiun' => $request->jenis_pensiun,
        ]);

        // Upload dokumen baru jika ada
        if ($request->hasFile('dokumen')) {
            foreach ($request->file('dokumen') as $jenisDokumen => $file) {
                // Hapus dokumen lama jika ada
                $oldDokumen = Dokumen::where('pengajuan_id', $pengajuan->id)
                    ->where('jenis_dokumen', $jenisDokumen)
                    ->first();

                if ($oldDokumen) {
                    Storage::delete($oldDokumen->path_file);
                    $oldDokumen->delete();
                }

                // Upload dokumen baru
                $uploadResult = $this->fileUploadService->uploadFile($file, 'pengajuan');

                Dokumen::create([
                    'pengajuan_id' => $pengajuan->id,
                    'jenis_dokumen' => $jenisDokumen,
                    'nama_file' => $uploadResult['original_name'],
                    'path_file' => $uploadResult['path'],
                    'ukuran_file' => $uploadResult['size'],
                    'mime_type' => $uploadResult['mime_type'],
                ]);
            }
        }

        // Log aktivitas
        LogAktivitas::create([
            'pengajuan_id' => $pengajuan->id,
            'user_id' => auth()->id(),
            'aktivitas' => 'Pengajuan diupdate',
            'keterangan' => 'Pengajuan diupdate oleh ' . auth()->user()->role
        ]);

        return redirect()->route('kanwil.pengajuan-kelola.show', $pengajuan->id)
            ->with('success', 'Pengajuan berhasil diupdate');
    }

    /**
     * Remove the specified pengajuan for Kanwil (superadmin only)
     */
    public function destroyKanwil(int $id): RedirectResponse
    {
        // Hanya Kanwil yang bisa hapus pengajuan
        if (auth()->user()->role !== 'Kanwil') {
            abort(403, 'Anda tidak memiliki akses untuk menghapus pengajuan.');
        }

        $pengajuan = PengajuanPensiun::findOrFail($id);

        // Hapus semua dokumen
        foreach ($pengajuan->dokumen as $dokumen) {
            Storage::delete($dokumen->path_file);
            $dokumen->delete();
        }

        // Hapus log aktivitas
        $pengajuan->logAktivitas()->delete();

        // Hapus pengajuan
        $pengajuan->delete();

        return redirect()->route('kanwil.pengajuan-kelola.index')
            ->with('success', 'Pengajuan berhasil dihapus');
    }
}
