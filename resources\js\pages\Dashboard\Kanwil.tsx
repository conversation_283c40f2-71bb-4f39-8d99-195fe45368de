import { Head } from '@inertiajs/react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { Search, Eye, CheckCircle, XCircle, Clock, FileText } from 'lucide-react'
import { useState } from 'react'
import { formatDate, getStatusBadgeVariant, getStatusLabel, getJenisPensiunLabel } from '@/lib/utils'
import { PengajuanPensiun } from '@/types'

interface DashboardStats {
  total_pengajuan: number
  menunggu_review: number
  disetujui_bulan_ini: number
  ditolak_bulan_ini: number
}

interface Props {
  pengajuan_list: {
    data: PengajuanPensiun[]
    current_page: number
    last_page: number
    total: number
  }
  statistik: DashboardStats
  filters: {
    status?: string
    jenis_pensiun?: string
  }
  status_options: string[]
  jenis_pensiun_options: string[]
}

export default function KanwilDashboard({ 
  pengajuan_list, 
  statistik, 
  filters, 
  status_options, 
  jenis_pensiun_options 
}: Props) {
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState(filters.status || 'all')
  const [jenisFilter, setJenisFilter] = useState(filters.jenis_pensiun || 'all')

  const handleFilterChange = () => {
    const params = new URLSearchParams()
    if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter)
    if (jenisFilter && jenisFilter !== 'all') params.append('jenis_pensiun', jenisFilter)
    if (search) params.append('search', search)

    window.location.href = `/kanwil/dashboard?${params.toString()}`
  }

  const handleResetFilter = () => {
    setStatusFilter('all')
    setJenisFilter('all')
    setSearch('')
    window.location.href = '/kanwil/dashboard'
  }

  const handleApproval = (id: number, action: 'approve' | 'reject') => {
    const catatan = action === 'reject' ? prompt('Catatan penolakan (opsional):') : ''
    
    const formData = new FormData()
    if (catatan) formData.append('catatan_penolakan', catatan)

    fetch(`/kanwil/pengajuan/${id}/${action}`, {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      }
    }).then(() => {
      window.location.reload()
    })
  }

  return (
    <>
      <Head title="Dashboard Kanwil" />
      <AppLayout title="Dashboard Kanwil">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard Kanwil</h1>
          <p className="text-gray-600">Review dan persetujuan pengajuan pensiun</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Pengajuan</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistik.total_pengajuan}</div>
              <p className="text-xs text-muted-foreground">Semua pengajuan</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Menunggu Review</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-600">{statistik.menunggu_review}</div>
              <p className="text-xs text-muted-foreground">Perlu ditindaklanjuti</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Disetujui Bulan Ini</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{statistik.disetujui_bulan_ini}</div>
              <p className="text-xs text-muted-foreground">Pengajuan disetujui</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ditolak Bulan Ini</CardTitle>
              <XCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{statistik.ditolak_bulan_ini}</div>
              <p className="text-xs text-muted-foreground">Pengajuan ditolak</p>
            </CardContent>
          </Card>
        </div>

        {/* Pengajuan Table */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Daftar Pengajuan Pensiun</CardTitle>
                <CardDescription>
                  Review dan berikan persetujuan untuk pengajuan pensiun
                </CardDescription>
              </div>
            </div>
            
            {/* Filters */}
            <div className="flex flex-wrap items-center gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari pegawai..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  {status_options.map((status) => (
                    <SelectItem key={status} value={status}>
                      {getStatusLabel(status)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={jenisFilter} onValueChange={setJenisFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Semua Jenis" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Jenis</SelectItem>
                  {jenis_pensiun_options.map((jenis) => (
                    <SelectItem key={jenis} value={jenis}>
                      {getJenisPensiunLabel(jenis)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button onClick={handleFilterChange}>Filter</Button>
              <Button variant="outline" onClick={handleResetFilter}>Reset</Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>NIP</TableHead>
                    <TableHead>Nama Pegawai</TableHead>
                    <TableHead>Jenis Pensiun</TableHead>
                    <TableHead>Tanggal Pengajuan</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Operator</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pengajuan_list.data.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                        Tidak ada data pengajuan
                      </TableCell>
                    </TableRow>
                  ) : (
                    pengajuan_list.data.map((pengajuan) => (
                      <TableRow key={pengajuan.id}>
                        <TableCell className="font-mono">{pengajuan.nip}</TableCell>
                        <TableCell className="font-medium">
                          {pengajuan.pegawai?.nama || 'N/A'}
                        </TableCell>
                        <TableCell>{getJenisPensiunLabel(pengajuan.jenis_pensiun)}</TableCell>
                        <TableCell>{formatDate(pengajuan.created_at)}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(pengajuan.status)}>
                            {getStatusLabel(pengajuan.status)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {pengajuan.user?.name || 'N/A'}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.location.href = `/kanwil/pengajuan/${pengajuan.id}`}
                              className="gap-1"
                            >
                              <Eye className="h-4 w-4" />
                              Detail
                            </Button>
                            
                            {pengajuan.status === 'Diajukan' && (
                              <>
                                <Button
                                  size="sm"
                                  onClick={() => handleApproval(pengajuan.id, 'approve')}
                                  className="gap-1 bg-green-600 hover:bg-green-700"
                                >
                                  <CheckCircle className="h-4 w-4" />
                                  Setujui
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => handleApproval(pengajuan.id, 'reject')}
                                  className="gap-1"
                                >
                                  <XCircle className="h-4 w-4" />
                                  Tolak
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
            
            {pengajuan_list.data.length > 0 && (
              <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                <div>
                  Menampilkan {pengajuan_list.data.length} dari {pengajuan_list.total} pengajuan
                </div>
                <div className="flex gap-2">
                  {pengajuan_list.current_page > 1 && (
                    <Button variant="outline" size="sm">
                      Previous
                    </Button>
                  )}
                  {pengajuan_list.current_page < pengajuan_list.last_page && (
                    <Button variant="outline" size="sm">
                      Next
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
    </>
  )
}
