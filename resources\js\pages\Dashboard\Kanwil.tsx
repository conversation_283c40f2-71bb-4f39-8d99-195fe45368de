import { Head } from '@inertiajs/react'
import AppLayout from '@/components/layouts/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { Search, Eye, CheckCircle, XCircle, Clock, FileText } from 'lucide-react'
import { useState } from 'react'
import { formatDate, getStatusBadgeVariant, getStatusLabel, getJenisPensiunLabel } from '@/lib/utils'
import { PengajuanPensiun } from '@/types'

interface DashboardStats {
  total_pengajuan: number
  menunggu_review: number
  disetujui_bulan_ini: number
  ditolak_bulan_ini: number
}

interface Props {
  pengajuan_list: {
    data: PengajuanPensiun[]
    current_page: number
    last_page: number
    total: number
  }
  statistik: DashboardStats
  filters: {
    status?: string
    jenis_pensiun?: string
  }
  status_options: string[]
  jenis_pensiun_options: string[]
}

export default function KanwilDashboard({ 
  pengajuan_list, 
  statistik, 
  filters, 
  status_options, 
  jenis_pensiun_options 
}: Props) {
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState(filters.status || 'all')
  const [jenisFilter, setJenisFilter] = useState(filters.jenis_pensiun || 'all')

  const handleFilterChange = () => {
    const params = new URLSearchParams()
    if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter)
    if (jenisFilter && jenisFilter !== 'all') params.append('jenis_pensiun', jenisFilter)
    if (search) params.append('search', search)

    window.location.href = `/kanwil/dashboard?${params.toString()}`
  }

  const handleResetFilter = () => {
    setStatusFilter('all')
    setJenisFilter('all')
    setSearch('')
    window.location.href = '/kanwil/dashboard'
  }

  const handleApproval = (id: number, action: 'approve' | 'reject') => {
    const catatan = action === 'reject' ? prompt('Catatan penolakan (opsional):') : ''
    
    const formData = new FormData()
    if (catatan) formData.append('catatan_penolakan', catatan)

    fetch(`/kanwil/pengajuan/${id}/${action}`, {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      }
    }).then(() => {
      window.location.reload()
    })
  }

  return (
    <>
      <Head title="Dashboard Kanwil" />
      <AppLayout title="Dashboard Kanwil">
      <div className="space-y-8 pb-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-800 mb-2">Dashboard Kanwil</h1>
          <p className="text-slate-600">Review dan persetujuan pengajuan pensiun</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Total Pengajuan</CardTitle>
              <div className="p-2 bg-blue-50 rounded-lg">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-800 mb-1">{statistik.total_pengajuan}</div>
              <p className="text-xs text-slate-500">Semua pengajuan</p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Menunggu Review</CardTitle>
              <div className="p-2 bg-orange-50 rounded-lg">
                <Clock className="h-4 w-4 text-orange-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600 mb-1">{statistik.menunggu_review}</div>
              <p className="text-xs text-slate-500">Perlu ditindaklanjuti</p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Disetujui Bulan Ini</CardTitle>
              <div className="p-2 bg-emerald-50 rounded-lg">
                <CheckCircle className="h-4 w-4 text-emerald-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-emerald-600 mb-1">{statistik.disetujui_bulan_ini}</div>
              <p className="text-xs text-slate-500">Pengajuan disetujui</p>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-sm bg-white hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
              <CardTitle className="text-sm font-semibold text-slate-700">Ditolak Bulan Ini</CardTitle>
              <div className="p-2 bg-red-50 rounded-lg">
                <XCircle className="h-4 w-4 text-red-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600 mb-1">{statistik.ditolak_bulan_ini}</div>
              <p className="text-xs text-slate-500">Pengajuan ditolak</p>
            </CardContent>
          </Card>
        </div>

        {/* Pengajuan Table */}
        <Card className="border-0 shadow-sm bg-white">
          <CardHeader className="border-b border-slate-100 bg-slate-50/50">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-slate-800">Daftar Pengajuan Pensiun</CardTitle>
                <CardDescription className="text-slate-600">
                  Review dan berikan persetujuan untuk pengajuan pensiun
                </CardDescription>
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-wrap items-center gap-4 pt-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Cari pegawai..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10 border-slate-200 focus:border-blue-500 focus:ring-blue-500/20"
                />
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px] border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  {status_options.map((status) => (
                    <SelectItem key={status} value={status}>
                      {getStatusLabel(status)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={jenisFilter} onValueChange={setJenisFilter}>
                <SelectTrigger className="w-[180px] border-slate-200 focus:border-blue-500 focus:ring-blue-500/20">
                  <SelectValue placeholder="Semua Jenis" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Jenis</SelectItem>
                  {jenis_pensiun_options.map((jenis) => (
                    <SelectItem key={jenis} value={jenis}>
                      {getJenisPensiunLabel(jenis)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                onClick={handleFilterChange}
                className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
              >
                Filter
              </Button>
              <Button
                variant="outline"
                onClick={handleResetFilter}
                className="border-slate-200 text-slate-600 hover:bg-slate-50"
              >
                Reset
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="border-b border-slate-100 bg-slate-50/30">
                    <TableHead className="font-semibold text-slate-700">NIP</TableHead>
                    <TableHead className="font-semibold text-slate-700">Nama Pegawai</TableHead>
                    <TableHead className="font-semibold text-slate-700">Jenis Pensiun</TableHead>
                    <TableHead className="font-semibold text-slate-700">Tanggal Pengajuan</TableHead>
                    <TableHead className="font-semibold text-slate-700">Status</TableHead>
                    <TableHead className="font-semibold text-slate-700">Operator</TableHead>
                    <TableHead className="text-right font-semibold text-slate-700">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pengajuan_list.data.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-12 text-slate-500">
                        <div className="flex flex-col items-center gap-2">
                          <FileText className="h-8 w-8 text-slate-300" />
                          <span>Tidak ada data pengajuan</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    pengajuan_list.data.map((pengajuan) => (
                      <TableRow key={pengajuan.id} className="border-b border-slate-50 hover:bg-slate-50/50 transition-colors">
                        <TableCell className="font-mono text-slate-600 py-4">{pengajuan.nip}</TableCell>
                        <TableCell className="font-medium text-slate-800 py-4">
                          {pengajuan.pegawai?.nama || 'N/A'}
                        </TableCell>
                        <TableCell className="text-slate-600 py-4">{getJenisPensiunLabel(pengajuan.jenis_pensiun)}</TableCell>
                        <TableCell className="text-slate-600 py-4">{formatDate(pengajuan.created_at)}</TableCell>
                        <TableCell className="py-4">
                          <Badge variant={getStatusBadgeVariant(pengajuan.status)} className="font-medium">
                            {getStatusLabel(pengajuan.status)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-slate-500 py-4">
                          {pengajuan.user?.name || 'N/A'}
                        </TableCell>
                        <TableCell className="text-right py-4">
                          <div className="flex justify-end gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.location.href = `/kanwil/pengajuan/${pengajuan.id}`}
                              className="gap-1 border-slate-200 text-slate-600 hover:bg-slate-50 hover:text-slate-800"
                            >
                              <Eye className="h-4 w-4" />
                              Detail
                            </Button>

                            {pengajuan.status === 'Diajukan' && (
                              <>
                                <Button
                                  size="sm"
                                  onClick={() => handleApproval(pengajuan.id, 'approve')}
                                  className="gap-1 bg-emerald-600 hover:bg-emerald-700 text-white shadow-sm"
                                >
                                  <CheckCircle className="h-4 w-4" />
                                  Setujui
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => handleApproval(pengajuan.id, 'reject')}
                                  className="gap-1 bg-red-600 hover:bg-red-700 text-white shadow-sm"
                                >
                                  <XCircle className="h-4 w-4" />
                                  Tolak
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
            
            {pengajuan_list.data.length > 0 && (
              <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                <div>
                  Menampilkan {pengajuan_list.data.length} dari {pengajuan_list.total} pengajuan
                </div>
                <div className="flex gap-2">
                  {pengajuan_list.current_page > 1 && (
                    <Button variant="outline" size="sm">
                      Previous
                    </Button>
                  )}
                  {pengajuan_list.current_page < pengajuan_list.last_page && (
                    <Button variant="outline" size="sm">
                      Next
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
    </>
  )
}
